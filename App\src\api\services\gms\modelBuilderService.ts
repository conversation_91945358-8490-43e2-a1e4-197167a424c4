import { IModelFormResponse } from "../../../features/program-admin/pages/program-setup/model/types";
import { IModelForm } from "../../../shared/components/common/form-builder/types";
import apiClient from "../../network/apiClient";

export const modelBuilderService = {
    /** get the model form data */
    async getById(id: number): Promise<IModelFormResponse> {
        const response = await apiClient.get<IModelFormResponse>(`${import.meta.env.VITE_PGM_API_BASE_URL}/api/models/${id}`)
        return response.data;
    },

    /** update model form */
    async updateModelForm(id: number, modelForm: IModelForm) {
        const response = await apiClient.put(`${import.meta.env.VITE_PGM_API_BASE_URL}/api/models/${id}/form-template`,
            modelForm
        );
        return response.data;
    },

    /** transform template */
    async transformTeamplate(formTemplate: IModelForm): Promise<Record<string, any>> {
        const response = await apiClient.post<Promise<any[]>>(`${import.meta.env.VITE_PGM_API_BASE_URL}/api/models/transform`,
            formTemplate
        );

        return (await response.data).reduce((obj, curr) => {
            obj[curr.Language] = curr.FormTemplate;
            return obj;
        }, {});
    },

    /** publish template */
    async publishTeamplate(id: number, formTemplate: IModelForm){
        await apiClient.post(`${import.meta.env.VITE_PGM_API_BASE_URL}/api/models/${id}/publish`,
            formTemplate
        );
    }

}