import React, { useState } from 'react';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';

interface Button {
    label: string;
    action: string | (() => void | Promise<void>);
    className?: string;
}

interface Step {
    title: string;
    component: React.ReactNode;
    buttons?: Button[];
    showPrev?: boolean;
    showNext?: boolean;
}

interface WizardProps {
    steps: Step[];
    onStepChange?: (stepIndex: number) => void;
}

const Wizard: React.FC<WizardProps> = ({ steps, onStepChange }) => {
    const [currentStep, setCurrentStep] = useState(0);

    const step = steps[currentStep];

    const handleAction = async (action: string | (() => void | Promise<void>)) => {
        if (typeof action === "function") {
            await action();
        } else if (action === "nextStep") {
            const nextStep = Math.min(currentStep + 1, steps.length - 1);
            setCurrentStep(nextStep);
            onStepChange?.(nextStep);
        } else if (action === "prevStep") {
            const prevStep = Math.max(currentStep - 1, 0);
            setCurrentStep(prevStep);
            onStepChange?.(prevStep);
        } else {
            console.warn(`Unknown action: ${action}`);
        }
    };

    return (
        <>
            {/* Step Header */}
            <div className="flex flex-wrap items-center gap-[10px] mb-[10px] bg-[#ffffff] px-[20px] py-[10px] rounded-[8px]">
                {steps.map((stepItem, index) => (
                    <React.Fragment key={index}>
                        <div className="relative flex items-center space-x-2">
                            <div className={`w-[30px] h-[30px] text-sm font-semibold flex items-center justify-center rounded relative ${index === currentStep ? 'bg-[#0B3456]' : 'bg-gray-300 cursor-pointer'}`}
                                onClick={() => { if (index !== currentStep) { setCurrentStep(index); onStepChange?.(index); } }}
                            >
                                <span className={`z-1 ${index === currentStep ? 'text-[#ffffff]' : 'text-[#0B3456]'}`}>
                                    {index + 1}
                                </span>
                                {/* Tail */}
                                {index === currentStep && (
                                    <div className="absolute bottom-[-3px] left-1/2 transform -translate-x-1/2 w-3 h-3 rotate-45 bg-[#0B3456]"></div>
                                )}
                            </div>
                            <span className={`text-sm ${index === currentStep ? 'font-bold text-[#0B3456]' : 'cursor-pointer'}`}
                                onClick={() => { if (index !== currentStep) { setCurrentStep(index); onStepChange?.(index); } }}
                            >
                                {stepItem.title}
                            </span>
                        </div>
                        {/* Vertical Spacer */}
                        {index < steps.length - 1 && <div className="w-[50px] h-[1px] bg-gray-300"></div>}
                    </React.Fragment>
                ))}
            </div>

            {/* Step Content */}
            <div className="mb-[10px] bg-[#ffffff] rounded-[8px] max-h-[calc(100vh-267px)] overflow-auto">{step.component}</div>

            {/* Button Panel */}
            <div className="flex justify-between items-center py-2 px-[20px] bg-[#ffffff] rounded-[8px] h-[50px]">
                <div className="flex space-x-4">
                    {step.showPrev !== false && currentStep !== 0 && (
                        <button
                            className="px-4 h-[30px] bg-[#00AEEF] hover:bg-[#0B3456] text-white rounded flex items-center gap-x-1"
                            onClick={() => handleAction("prevStep")}
                        >
                            <span className="leading-none mb-[3px] mr-[15px]">Previous</span>
                            <LeftOutlined className="text-[11px]" />
                        </button>
                    )}

                    {step.showNext !== false && currentStep !== steps.length - 1 && (
                        <button
                            className="px-4 h-[30px] bg-[#00AEEF] hover:bg-[#0B3456] text-white rounded flex items-center gap-x-1"
                            onClick={() => handleAction("nextStep")}
                        >
                            <span className="leading-none mb-[3px] mr-[15px]">Next</span>
                            <RightOutlined className="text-[11px]" />
                        </button>
                    )}

                    {step.buttons?.map((btn) => (
                        <button
                            key={btn.label}
                            className={btn.className || "px-4 h-[30px] bg-[#00AEEF] hover:bg-[#0B3456] text-white rounded flex items-center gap-x-1"}
                            onClick={() => handleAction(btn.action)}
                        >
                            <span className="leading-none mb-[3px] mr-[15px]">{btn.label}</span>
                        </button>
                    ))}
                </div>
            </div>
        </>
    );
};

export default Wizard;