import { IFormElement } from "../types";

export const moveElementUp = (element: IFormElement, elements: IFormElement[], setElements: (e: IFormElement[]) => void) => {
    const idx = elements.findIndex(el => el.elementId === element.elementId);
    if (idx > 0) {
        const updated = [...elements];
        [updated[idx - 1], updated[idx]] = [updated[idx], updated[idx - 1]];
        setElements(updated);
    }
};

export const moveElementDown = (element: IFormElement, elements: IFormElement[], setElements: (e: IFormElement[]) => void) => {
    const idx = elements.findIndex(el => el.elementId === element.elementId);
    if (idx !== -1 && idx < elements.length - 1) {
        const updated = [...elements];
        [updated[idx], updated[idx + 1]] = [updated[idx + 1], updated[idx]];
        setElements(updated);
    }
};
