import React from "react";
import { Select } from "antd";
import { DeleteOutlined } from "@ant-design/icons";

type Option = {
    value: string;
    label: string;
};

type ModelsInfoProps = {
    modelOptions: Option[];
    modelsInfo: { model: string; displayName: string }[];
    setModelsInfo: React.Dispatch<React.SetStateAction<{ model: string; displayName: string }[]>>;
};

const ModelsInfo: React.FC<ModelsInfoProps> = ({ modelOptions, modelsInfo, setModelsInfo }) => {
    const handleModelChange = (index: number, value: string) => {
        const selectedOption = modelOptions.find(option => option.value === value);
        const updated = [...modelsInfo];
        updated[index].model = value;
        updated[index].displayName = selectedOption?.label || "";
        setModelsInfo(updated);
    };

    const handleDisplayNameChange = (index: number, value: string) => {
        const updated = [...modelsInfo];
        updated[index].displayName = value;
        setModelsInfo(updated);
    };

    const handleAddRow = () => {
        setModelsInfo([...modelsInfo, { model: '', displayName: '' }]);
    };

    const handleDeleteRow = (index: number) => {
        if (modelsInfo.length > 1) {
            const updated = [...modelsInfo];
            updated.splice(index, 1);
            setModelsInfo(updated);
        }
    };

    return (
        <div className="p-[20px] w-[800px]">
            <h2 className="text-[20px] leading-[20px] text-[#0B3456] font-semibold mb-4">Program Models</h2>
            <div className="flex text-[#0B3456] font-medium mb-2">
                <div className="w-1/2 pr-2">Model</div>
                <div className="w-1/2 px-2">Display Name</div>
            </div>
            <div className="space-y-4">
                {modelsInfo.map((row, index) => (
                    <div key={index} className="flex items-center gap-2">
                        <div className="w-1/2 pr-2">
                            <Select
                                showSearch
                                allowClear
                                value={row.model || undefined}
                                onChange={(value) => handleModelChange(index, value || "")}
                                options={modelOptions}
                                style={{ width: "100%", height: 40 }}
                                dropdownStyle={{ zIndex: 2000 }}
                                className="rounded border"
                                placeholder="Select Model"
                                filterOption={(input, option) =>
                                    (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
                                }
                            />
                        </div>
                        <div className="w-1/2 px-2">
                            <input
                                type="text"
                                className="w-full rounded px-3 py-2 border h-[40px]"
                                value={row.displayName}
                                onChange={(e) => handleDisplayNameChange(index, e.target.value)}
                            />
                        </div>
                        <div className="pl-1">
                            <DeleteOutlined
                                onClick={() => {
                                    if (modelsInfo.length > 1) {
                                        handleDeleteRow(index);
                                    }
                                }}
                                style={{
                                    color: modelsInfo.length <= 1 ? "#CCCCCC" : "#0B3456",
                                    cursor: modelsInfo.length <= 1 ? "not-allowed" : "pointer",
                                    fontSize: "18px",
                                }}
                            />
                        </div>
                    </div>
                ))}
            </div>
            <div className="mt-6">
                <button
                    className="px-4 h-[30px] bg-[#00AEEF] hover:bg-[#0B3456] text-white rounded flex items-center gap-x-1"
                    onClick={handleAddRow}
                >
                    Add
                </button>
            </div>
        </div>
    );
};

export default ModelsInfo;