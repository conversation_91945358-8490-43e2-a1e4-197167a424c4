import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { <PERSON><PERSON><PERSON>, JFormSchema } from "../../../../shared/components/common/json-form";
import { message } from "antd";
import { Loader } from "../../../../shared/components/common";
import { userGroupService, UserGroup } from "../../../../api/services/admin/usergroupService";
import UserGroupMappingList from "./UserGroupMappingList";

const UserGroupForm: React.FC<{ mode: "create" | "edit" }> = ({ mode }) => {
  const navigate = useNavigate();
  const { id } = useParams();
  const userGroupId = id ? Number(id) : null;
  const [formData, setFormData] = useState<UserGroup | null>(null);

  const schema: JFormSchema = {
    title: `${mode} User Group Management`,
    formGroups: [
      {
        title: "",
        columns: 2,
        fields: [
          {
            name: "Id",
            type: "hidden",
            required: true,
            defaultValue:0
          },
          {
            name: "Code",
            type: "text",
            label: "Code",
            required: true,
            validation: {
              required: true,
              maxLength: 5
            }
          },
          {
            name: "Name",
            type: "text",
            label: "Name",
            required: true,
            validation: {
              required: true,
              maxLength: 100
            }
          },
          {
            name: "Email",
            type: "email",
            label: "Email",
            required: true,
            validation: {
              required: true,
              pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
              maxLength: 150
            }
          }
        ]
      }
    ]
  };

  useEffect(() => {
    if (mode === "edit" && userGroupId) {
      userGroupService.getById(userGroupId)
      .then(setFormData)
      .catch((error) => console.error("Error fetching user group data:", error));
    }
  },  [mode, userGroupId]);

  const handleSubmit = async (formValues: Record<string, any>) => {
    try {
      const response=mode === "edit" && userGroupId 
        ? await userGroupService.update(userGroupId, formValues):
        await userGroupService.create(formValues);
        if (response) {
          message.success(`User group ${mode === "edit" ? "updated" : "created"} successfully.`);
          navigate("..");
        } else {
          message.error(`Failed to ${mode === "edit" ? "update" : "create"} user group. Please try again.`);
        }
      } catch (error: any) {
        console.error("Error submitting form:", error);
        message.error(error?.response?.data?.message || "An unexpected error occurred.");
      }
    };
  

  return (
    <>
      <div className="flex flex-col gap-2 bg-white p-[30px] rounded-[6px]">
        {mode === "edit" && !formData ? (
          <Loader />
        ) : (
          <JForm schema={schema} onSubmit={handleSubmit} data={formData || {}} />
        )}
      </div>

      <div className="mt-[15px]">
        {id && <UserGroupMappingList userGroupId={id} />}
      </div>
    </>
  );
};

export default UserGroupForm;
