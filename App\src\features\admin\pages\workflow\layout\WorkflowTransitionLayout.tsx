import React, { useState } from "react";
import WorkflowTransition from "../components/Transition";
import WorkflowTransitionForm from "../components/TransitionAddForm";

const WorkflowTransitionLayout: React.FC = () => {
  const [showForm, setShowForm] = useState(false);

  return (
    <div className="workflow-transition-layout">
      {showForm ? (
        <WorkflowTransitionForm  />
      ) : (
        <WorkflowTransition onAdd={() => setShowForm(true)} />
      )}
    </div>
  );
};

export default WorkflowTransitionLayout;
