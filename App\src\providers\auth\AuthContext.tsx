import React, { createContext, useState, ReactNode, useContext, useEffect } from 'react';
import { authProvider } from './authProviderFactory';
import { AuthContextType, AuthCredential, AuthProviderType, AuthUser } from './authContext.types';
import { setAuthToken } from "../../api/utils/axiosInstance";

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [authType, setAuthType] = useState<AuthProviderType | null>(null);
  const [user, setUser] = useState<AuthUser | null>(null);
  const [authInProgress, setAuthInProgress] = useState(true);

  useEffect(() => {
    authProvider.getUser().then((user) => {
      setUser(user);
      setAuthInProgress(false);
      setAuthToken(user?.access_token ?? null)
    });
  }, []);

  const login = async (credentials?: AuthCredential): Promise<void> => {
    await authProvider.login((eventType, data) => {
      if (eventType === "LOGIN_SUCCESS") {
        setUser(data || null);
        setAuthType(authType)
      } else if (eventType == "LOGIN_FAILURE") {
        setUser(null);
        setAuthType(null);
        throw data;
      }
    }, credentials);
  };

  const logout = async () => {
    await authProvider.logout((eventType) => {
      if (eventType === "LOGOUT_SUCCESS") {
        setUser(null);
        setAuthType(null);
      }
    })
  };

  const value: AuthContextType = {
    authType,
    authInProgress,
    user,
    login,
    logout
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export { AuthContext, AuthProvider };

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);

  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }

  return context;
};
