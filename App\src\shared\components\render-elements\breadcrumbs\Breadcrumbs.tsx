import React from "react";
import { <PERSON>, use<PERSON>atch<PERSON>, use<PERSON><PERSON><PERSON>, RouteMatch, Params } from "react-router-dom";

// Define the shape for a breadcrumb on a route's handle.
interface BreadcrumbHandle {
  breadcrumb: string | ((data: { params: Params<string> }) => string);
}

// Extend RouteMatch to optionally include our custom handle.
interface BreadcrumbRouteMatch extends RouteMatch {
  handle?: BreadcrumbHandle;
}

/**
 * Returns a friendly label for a given route match.
 * If a handle exists, its breadcrumb is used (calling it if it’s a function);
 * otherwise, the last segment of the pathname is used.
 */
const getBreadcrumbLabel = (match: BreadcrumbRouteMatch): string => {
  if (match.handle?.breadcrumb) {
    return typeof match.handle.breadcrumb === "function"
      ? match.handle.breadcrumb({ params: match.params })
      : match.handle.breadcrumb;
  }
  // Fallback: use the last non-empty segment from the pathname or default to "Dashboard"
  const segments = match.pathname.split("/").filter(Boolean);
  return segments.length ? decodeURIComponent(segments[segments.length - 1]) : "Dashboard";
};

const Breadcrumbs: React.FC = () => {
  // Cast useMatches return value to our extended BreadcrumbRouteMatch type.
  const matches = useMatches() as unknown as BreadcrumbRouteMatch[];
  const navigate = useNavigate();

  // Deduplicate consecutive matches with identical breadcrumb labels.
  const uniqueMatches = matches.filter((match, index, arr) => {
    if (index === 0) return true;
    return getBreadcrumbLabel(match) !== getBreadcrumbLabel(arr[index - 1]);
  });

  return (
    <nav
      aria-label="Breadcrumb"
      className="breadcrumbs flex flex-row sticky top-[60px] justify-between text-gray-600 text-sm px-4 py-2 mb-2 m-[-16px]"
    >
      <ol className="flex space-x-2">
        {location.pathname !== '/' && (
          <li className="flex items-center space-x-2">
            <Link to={'/'} className="text-blue-500 hover:underline capitalize">
              Dashboard
            </Link>
            <span className="text-[#ccc]">/</span>
          </li>
        )}
        {uniqueMatches.map((match, index) => {
          const isLast = index === uniqueMatches.length - 1;
          const label = getBreadcrumbLabel(match);

          return (
            <li key={`${match.pathname}-${index}`} className="flex items-center space-x-2">
              {index > 0 && <span className="text-[#ccc]">/</span>}
              {isLast ? (
                <span className="capitalize">{label}</span>
              ) : (
                <Link to={match.pathname} className="text-blue-500 hover:underline capitalize">
                  {label}
                </Link>
              )}
            </li>
          );
        })}
      </ol>
      <button
        disabled={uniqueMatches.length <= 1}
        onClick={() => navigate(-1)}
        className={`flex justify-center px-2 py-0.5 text-[12px] rounded-md transition-all ${uniqueMatches.length <= 1
          ? "bg-gray-300 text-gray-500 cursor-not-allowed"
          : "bg-gray-800 text-white hover:bg-white hover:text-gray-800 active:scale-95"
          }`}
      >
        ← Back
      </button>
    </nav>
  );
};

export default Breadcrumbs;
