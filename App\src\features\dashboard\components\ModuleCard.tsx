import React from "react";

interface ModuleCardProps {
  module: any;
  onSelect: (id: string) => void;
}

const ModuleCard: React.FC<ModuleCardProps> = ({ module, onSelect }) => {
  return (
    <div
      className="flex justify-center items-center w-[260px] max-w-sm h-[200px] text-center p-6 bg-white rounded-lg shadow-md cursor-pointer hover:shadow-lg transition-shadow uppercase"
      onClick={() => onSelect(module.Id ?? module.ModuleName)}
    >
      <h3 className="text-lg font-bold">{module.Name ?? module.ModuleName}</h3>
    </div>
  );
};

export default ModuleCard;
