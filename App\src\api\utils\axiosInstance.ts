import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from "axios";
import AuthManager from "../../shared/utils/authManager";
// import axiosRetry from "axios-retry"; // Retry library for network resilience

// Axios configuration
const API_CONFIG: AxiosRequestConfig = {
  // baseURL: `${import.meta.env.VITE_AUTH_API_BASE_URL}/api`,
  headers: {
    Accept: "application/json",
    "Content-Type": "application/json",
    "Tenant": localStorage.getItem("tenant")
  },
  responseType: "json",
};

// Create Axios instance
const axiosInstance = axios.create(API_CONFIG);

// Retry mechanism
// axiosRetry(axiosInstance, {
//   retries: 3, // Number of retries
//   retryCondition: (error: AxiosError) =>
//     !error.response || error.response.status >= 500, // Retry on network issues or server errors
//   retryDelay: (retryCount: number) => retryCount * 1000, // Exponential backoff (1000ms, 2000ms, 3000ms, etc.)
// });

// Function to set the Authorization token dynamically
export const setAuthToken = (token: string | null): void => {
  if (token) {
    axiosInstance.defaults.headers.common["Authorization"] = `Bearer ${token}`;
  } else {
    delete axiosInstance.defaults.headers.common["Authorization"];
  }
};

axiosInstance.interceptors.request.use(async (config) => {
  return config;
}, (error) => {
  // Handle request errors
  return Promise.reject(error);
});

// Response interceptor for handling errors and responses
axiosInstance.interceptors.response.use(
  // Success response
  (response: AxiosResponse) => {
    return response; // Pass response through without modification
  },

  // Error handling
  async (error: AxiosError) => {
    if (error.response) {
      const { status } = error.response;

      // Handle authentication fail (401)
      if (status === 401) {
        await AuthManager.logout();
      }

      // Handle Forbidden (403)
      if (status === 403) {
        console.error("Access denied: You don't have permission for this resource.");
      }

      // Handle Not Found (404)
      if (status === 404) {
        console.error("Resource not found: The requested resource does not exist.");
      }

      // Handle Internal Server Error (500)
      if (status === 500) {
        console.error("Server error: Something went wrong on the server.");
      }
    } else {
      // Handle generic network or unexpected errors
      console.error("Network error or an unexpected issue occurred.");
    }

    return Promise.reject(error);
  }
);

export default axiosInstance;
