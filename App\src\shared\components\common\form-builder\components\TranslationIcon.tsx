import React, { useState } from 'react';
import icnTranslate from "../assets/icn_translate.png";
import icnHover from "../assets/icn_hover.png";

interface TranslationIconProps {
    onClick?: () => void;
    className?: string;
}

const TranslationIcon: React.FC<TranslationIconProps> = ({ onClick, className }) => {
    const [isHovered, setIsHovered] = useState(false);

    return (
        <div
            className={`cursor-pointer flex items-center ml-[10px] ${className ?? ''}`}
            onClick={onClick}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
        >
            <img
                src={isHovered ? icnHover : icnTranslate}
                alt="Translation Icon"
                className="w-[35px] h-[25px]"
            />
        </div>
    );
};

export default TranslationIcon;
