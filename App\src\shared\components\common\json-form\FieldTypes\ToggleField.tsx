import React from "react";
import { FormField } from "../types";
import styles from "./styles.module.css";

interface ToggleFieldProps {
  field: FormField;
  value: boolean;
  onChange: (name: string, value: boolean) => void;
}

const ToggleField: React.FC<ToggleFieldProps> = ({ field, value, onChange }) => {
  return (
    <div className={styles.toggleField}>
      <label className={styles.switch}>
        <input
          type="checkbox"
          checked={value}
          onChange={(e) => onChange(field.name, e.target.checked)}
        />
        <span className={styles.slider}></span>
      </label>
    </div>
  );
};

export default ToggleField;
