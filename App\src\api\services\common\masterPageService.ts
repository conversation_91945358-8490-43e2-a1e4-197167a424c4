import apiClient from "../../network/apiClient";

// Define the API Response structure ensuring Record & UserRights are always present
interface ApiResponse<T> {
  Record: T[];
  UserRights: string;
  [key: string]: unknown; // Allows additional keys based on different screens
}

interface ApiService<T> {
  fetchAll: () => Promise<ApiResponse<T>>;
  create: (data: Partial<T>) => Promise<T>;
  update: (id: string, data: Partial<T>) => Promise<T>;
  delete: (id: string) => Promise<void>;
}

const createApiService = <T>(endpoint: {
  create?: string;
  edit?: string;
  delete?: string;
  list?: string;
}): ApiService<T> => ({
  async fetchAll(): Promise<ApiResponse<T>> {
    const response = await apiClient.get<ApiResponse<T>>(endpoint.list ?? '');
    return response.data;
  },

  async create(data: Partial<T>): Promise<T> {
    const response = await apiClient.post<T>(endpoint.create ?? '', data);
    return response.data;
  },

  async update(id: number | string, data: Partial<T>): Promise<T> {
    const response = await apiClient.put<T>(`${endpoint.edit}/${id}`, data);
    return response.data;
  },

  async delete(id: number | string): Promise<void> {
    await apiClient.delete(`${endpoint.delete}/${id}`);
  },
});

export default createApiService;
