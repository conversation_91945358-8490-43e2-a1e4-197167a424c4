import React from "react";
import { MasterPage } from "../../../../../shared/components/render-elements";
import { ColumnSchema } from "../../../../../shared/components/common/data-table";

const frameworkSchema: ColumnSchema[] = [
  {
    key: "Id",
    title: "Id",
    hidden: true,
    editable: false,
    sorter: false
  },
  {
    key: "Name",
    title: "Name",
    editable: true,
    sorter: true,
    type: "text",
    filterable: true
  },
  {
    key: "FrameworkTypeId",
    title: "Framework Type",
    editable: true,
    sorter: true,
    type: "select",
    filterable: true
  }
];

const FrameworkList: React.FC = () => {
  return <MasterPage
    entityName="Framework"
    columnsSchema={frameworkSchema}
    mode="form"
    apiEndpoints={{
      create: `${import.meta.env.VITE_PGM_API_BASE_URL}/api/framework`,
      edit: `${import.meta.env.VITE_PGM_API_BASE_URL}/api/framework`,
      delete: `${import.meta.env.VITE_PGM_API_BASE_URL}/api/framework`,
      list: `${import.meta.env.VITE_PGM_API_BASE_URL}/api/framework`
    }}
    routeTemplates={{
      create: "/admin/application/frameworks/create",
      edit: "/admin/application/frameworks/:id/edit",
    }}
  />;
}

export default FrameworkList;
