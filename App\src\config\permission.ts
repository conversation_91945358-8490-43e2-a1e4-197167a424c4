export const ROLES = {
  champion: "champion",
  unitHead: "unit_head",
  admin: "admin",
  legalCounsel: "legal_counsel"
};

export const SCOPES = {
  resource: {
    canSubmit: "can-submit",
    canView: "can-view",
    canApprove: "can-approve",
    canPublicApprove: "can-public-approve",
    canFeature: "can-feature"
  },
  setting: {
    manageUser: "manage-user",
    delegateUser: "delegate-user",
    manageNotification: "manage-notification"
  }
};

export const PERMISSIONS = {
  [ROLES.champion]: [SCOPES.resource.canSubmit, SCOPES.resource.canView],
  [ROLES.unitHead]: [SCOPES.resource.canView, SCOPES.resource.canApprove],
  [ROLES.admin]: [
    SCOPES.resource.canView,
    SCOPES.resource.canSubmit,
    SCOPES.resource.canApprove,
    SCOPES.setting.manageUser,
    SCOPES.setting.delegateUser,
    SCOPES.setting.manageNotification
  ]
};
