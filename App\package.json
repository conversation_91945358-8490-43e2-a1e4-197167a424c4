{"name": "UNAIDSapp", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start:dev": "vite --mode development --port 5001", "build:dev": "tsc -b && vite build --mode development", "build:integration": "tsc -b && vite build --mode integration", "build:testing": "tsc -b && vite build --mode testing", "build:staging": "tsc -b && vite build --mode staging", "build:production": "tsc -b && vite build --mode production", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@azure/msal-browser": "^4.8.0", "@azure/msal-react": "^3.0.7", "@tailwindcss/vite": "^4.0.14", "antd": "^5.24.6", "axios": "^1.8.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.3.0", "react-toastify": "^11.0.5", "tailwindcss": "^4.0.14"}, "overrides": {"@azure/msal-react": {"react": "^16.8.0 || ^17 || ^18 || ^19"}}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "prettier": "^3.5.3", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}