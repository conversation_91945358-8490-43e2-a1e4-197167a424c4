import apiClient from "../../network/apiClient";
import { GenericErrorResponse } from "../../utils/errorHandler";

interface AuthResponse {
  id_token: string;
  access_token: string;
  refresh_token: string;
}

export const authService = {
  /**
   * Login the user.
   * @param username - Username.
   * @param password - User's password.
   * @returns A promise resolving to access and refresh tokens.
   */
  async login(username: string, password: string): Promise<AuthResponse> {
    try {
      const redirectUri = import.meta.env.VITE_CUSTOM_JWT_REDIRECT_URI;
      const clientId = import.meta.env.VITE_CUSTOM_JWT_CLIENT_ID;
      const scope = import.meta.env.VITE_CUSTOM_JWT_SCOPE;

      const response = await apiClient.post<AuthResponse>(
        `/account/authorize?clientId=${clientId}&redirectUri=${redirectUri}&scope=${scope}`,
        { username, password },
        { baseURL: `${import.meta.env.VITE_CUSTOM_JWT_LOGIN_URL}/api` }
      );

      return response.data;
    } catch (error) {
      const knownError = error as GenericErrorResponse<any>;

      if (knownError.status === 401) {
        const errorMessage = knownError.data as { error: string, error_description: string };
        throw new Error(errorMessage.error_description);
      } else {
        const errorMessage = (knownError.data as { message: string }).message;
        throw new Error(errorMessage);
      }
    }
  },

  /**
   * Logout the user.
   * Clears access and refresh tokens from local storage.
   */
  logout(): void {
    console.log("User logged out successfully.");
  },

  /**
   * Refresh the access token using the refresh token.
   * @returns A promise resolving to a new access token.
   */
  async refreshToken(): Promise<string> {
    const refreshToken = localStorage.getItem("refreshToken");

    if (!refreshToken) throw new Error("Refresh token not found. Please login again.");

    try {
      const response = await apiClient.post<{ accessToken: string }>(`${""}/refresh-token`, {
        refreshToken,
      });

      const { accessToken } = response.data;

      // Update access token in local storage

      return accessToken;
    } catch (error) {
      throw new Error("Failed to refresh token. Please login again.");
    }
  },

  /**
   * Retrieve the current access token from local storage.
   * @returns The access token or null if not found.
   */
  getToken(): string | null {
    return localStorage.getItem("accessToken");
  },

  /**
   * Check if the access token is expired.
   * @param token - The JWT token.
   * @returns True if the token is expired, false otherwise.
   */
  isTokenExpired(token: string): boolean {
    const payload = JSON.parse(atob(token.split(".")[1]));
    const currentTime = Math.floor(Date.now() / 1000); // Current time in seconds
    return payload.exp < currentTime;
  },
};
