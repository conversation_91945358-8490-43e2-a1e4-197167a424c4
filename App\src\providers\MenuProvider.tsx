import { createContext, useContext, useEffect, useState } from "react";
import { appSettingService, MenuResult } from "../api/services/common/appSettingService";
import { useAuth } from "./auth/AuthContext";

const MenuContext = createContext<MenuResult | null>(null);

export const useMenu = () => useContext(MenuContext);

export const MenuProvider = ({ children }: { children: React.ReactNode }) => {
  const { user } = useAuth();
  const [menuData, setMenuData] = useState<MenuResult | null>(null);

  useEffect(() => {
    const fetchAllMenu = async () => {
      try {
        const response = await appSettingService.fetchMenu();
        setMenuData(response);
        // Set defaut tenant
        if (!localStorage.getItem("tenant") && response?.Tenants) {
          localStorage.setItem("tenant", response?.Tenants[0].Value.toString());
        }
      } catch (error) {
        console.error("Error fetching user data:", error);
      }
    };

    if (user) {
      fetchAllMenu();
    }
  }, [user]);

  return <MenuContext.Provider value={menuData}>{children}</MenuContext.Provider>;
};
