import React from "react";
import { MasterPage } from "../../../../shared/components/render-elements";
import { ColumnSchema } from "../../../../shared/components/common/data-table";

const roleSchema: ColumnSchema[] = [
  {
    key: "Id",
    title: "Id",
    hidden: true,
    editable: false,
    sorter: false
  },
  {
    key: "Code",
    title: "Code",
    editable: true,
    sorter: true,
    type: "text",
    filterable: true
  },
  {
    key: "Name",
    title: "Name",
    editable: true,
    sorter: true,
    type: "text",
    filterable: true
  },
  {
    key: "Level",
    title: "Level",
    editable: true,
    sorter: true,
    type: "number",
    filterable: true
  }
];

const RoleList: React.FC = () => {
  return <MasterPage
    entityName="Role"
    columnsSchema={roleSchema}
    mode="inline"
    apiEndpoints={{
      create: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/roles`,
      edit: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/roles`,
      delete: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/roles`,
      list: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/roles`
    }}
  />;
}

export default RoleList;
