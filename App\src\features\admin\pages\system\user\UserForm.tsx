import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { FormOption, JForm, JFormSchema } from "../../../../../shared/components/common/json-form";
import { roleService, userService } from "../../../../../api";
import { User } from "../../../../../api/services/admin/userService";
import { message } from "antd";
import { Loader } from "../../../../../shared/components/common";
import UserPermissionList from "./Userpermission";

const UserForm: React.FC<{ mode: "create" | "edit" }> = ({ mode }) => {
  const navigate = useNavigate();
  const { id } = useParams(); // Get user ID from URL params for edit mode
  const [formData, setFormData] = useState<User | null>(null);
  const userId = Number(id);

  useEffect(() => {
    if (mode === "edit" && userId) {
      userService.getById(userId)
        .then(setFormData)
        .catch((error) => console.error("Error fetching user data:", error));
    }
  }, [mode, userId]);

  const handleSubmit = async (formValues: Record<string, any>) => {
    try {
      const response = mode === "edit" && userId
        ? await userService.update(userId, formValues)
        : await userService.create(formValues);

      if (response) {
        message.success(`User ${mode === "edit" ? "updated" : "created"} successfully.`);
        navigate("..");
      } else {
        message.error(`Failed to ${mode === "edit" ? "update" : "create"} user. Please try again.`);
      }
    } catch (error: any) {
      console.error("Error submitting form:", error);
      message.error(error?.response?.data?.message || "An unexpected error occurred.");
    }
  };

  const schema: JFormSchema = {
    title: `${mode} User`,
    formGroups: [
      {
        title: "",
        columns: 3,
        fields: [
          {
            name: "Id",
            type: "hidden",
            required: true,
            defaultValue: 0
          },
          {
            name: "UserName",
            type: "text",
            label: "User Name",
            required: true
          },
          {
            name: "LoginName",
            type: "text",
            label: "Login Name",
            required: true
          },
          {
            name: "Email",
            type: "email",
            label: "Email",
            required: true,
            validation: {
              required: true,
              pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
            },
          },
          {
            name: "EffectiveFrom",
            type: "date",
            label: "Effective From",
            required: true
          },
          {
            name: "RoleId",
            type: "select",
            label: "Role",
            required: true,
            options: async (): Promise<FormOption[]> => {
              const response = await roleService.fetchAll();
              return response.Record.map(role => ({
                label: role.Name,
                value: role.Id
              }));
            },
          },
        ]
      }
    ]
  };

  return (
    <>
    <div className="flex flex-col gap-2 bg-white p-[30px] rounded-[6px]">
      {mode === "edit" && !formData ? (
        <Loader />
      ) : (
        <JForm schema={schema} onSubmit={handleSubmit} data={formData || {}} />
      )}
    </div>
    
    <div className="mt-[15px]">
        {userId !== null && <UserPermissionList userId ={userId} />}
      </div>
   
  </>
  );
};

export default UserForm;
