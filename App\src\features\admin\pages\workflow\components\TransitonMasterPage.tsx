import { useEffect, useState, useCallback } from 'react';
import createApiService from '../../../../../api/services/common/masterPageService';
import { ColumnSchema, DataTable, OptionItem } from '../../../../../shared/components/common/data-table';
import WorkflowTransitionForm from './TransitionAddForm';
interface TransitionMasterPageProps<T extends { Id: string }> {
    entityName: string;
    columnsSchema: ColumnSchema[];
    mode?: 'form'
    apiEndpoints: {
        delete?: string;
        list?: string;
    };
    onEdit?: (record: T) => void;
    onDelete?: (id: string) => void;
}

const TransitionMasterPage = <T extends { Id: string }>({
    entityName,
    columnsSchema,
    apiEndpoints,
    onEdit,
    onDelete,
}: TransitionMasterPageProps<T>) => {
    const apiService = createApiService<T>(apiEndpoints);
    const [data, setData] = useState<Array<T & { key: string }>>([]);
    const [options, setOptions] = useState<{ [key: string]: OptionItem[] }>({});
    const [showForm, setShowForm] = useState(false);
    // Removed unused editingRecord state

    useEffect(() => {
        loadData();
    }, []);

    const loadData = useCallback(async () => {
        const fetchedData = await apiService.fetchAll();
        const recordsArray = fetchedData.Record;

        const mappedData = recordsArray.map(item => ({
            ...item,
            key: item.Id,
        }));

        const matchingKeys = columnsSchema
            .map(col => col.key)
            .filter(key => fetchedData.hasOwnProperty(key));

        const extractedOptions: { [key: string]: OptionItem[] } = {};
        matchingKeys.forEach((key) => {
            if (Array.isArray(fetchedData[key])) {
                extractedOptions[key] = fetchedData[key].map((item: any) => ({
                    value: item.Value,
                    label: item.DisplayText || item.Value.toString(),
                }));
            }
        });

        setOptions(extractedOptions);
        setData(mappedData);
    }, [apiService]);

    const handleDelete = async (key: string) => {
        await apiService.delete(key);
        await loadData();
        onDelete?.(key);
    };

    const handleEdit = (key: string) => {
        const record = data.find((item) => item.Id === key);
        if (record) {
            // Removed setEditingRecord as editingRecord is no longer used
            setShowForm(true);
            onEdit?.(record);
        }
    };

    const handleAdd = () => {
        // Removed setEditingRecord as editingRecord is no longer used
        setShowForm(true); // show the form
    };

    const updatedColumnsSchema = columnsSchema.map((col) => ({
        ...col,
        options: col.options || options[col.key] || [],
    }));

    return (
        <div className="flex flex-col gap-2 h-full bg-white p-[30px] rounded-[6px]">
            {showForm ? (
                <WorkflowTransitionForm
                />
            ) : (
                <DataTable
                    header={entityName}
                    data={data}
                    inlineEditing={false}
                    columnsSchema={updatedColumnsSchema}
                    onEdit={handleEdit}
                    onAdd={handleAdd}
                    onDelete={handleDelete}
                />
            )}
        </div>
    );
};

export default TransitionMasterPage;
