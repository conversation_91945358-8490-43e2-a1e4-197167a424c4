import React from "react";
import { ColumnSchema } from "../../../../../shared/components/common/data-table/types";
import TransitionMasterPage from "./TransitonMasterPage";

interface Props {
  onAdd: () => void;
}

const workflowTransitionSchema: ColumnSchema[] = [
  {
    key: "Id",
    title: "Id",
    hidden: true,
    sorter: false
  },
  {
    key: "From",
    title: "From",
    sorter: true,
    type: "text",
    filterable: false
  },
  {
    key: "To",
    title: "To",
    type: "text",
    filterable: false
  },
];

const WorkflowTransition: React.FC<Props> = () => {
  return (
    <TransitionMasterPage
      entityName="Workflow Transition"
      mode="form"
      columnsSchema={workflowTransitionSchema}
      apiEndpoints={{
        list: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/workflow-transition`,
      }}
    />
  );
};

export default WorkflowTransition;
