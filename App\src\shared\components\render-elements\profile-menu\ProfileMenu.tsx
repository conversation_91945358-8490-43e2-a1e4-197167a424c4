import React, { forwardRef } from "react";
import { <PERSON> } from "react-router-dom";
import { Avatar } from "../../common";
import withClickOutside from "../../../hoc/withClickOutside";
import ProfileIcon from "./components/ProfileIcon";
import LogOutIcon from "./components/LogOutIcon";
import { useAuth } from "../../../../providers/auth/AuthContext";
import TenantSwitcher from "./components/TenantSwitcher";

type Props = {
  isOpen: boolean,
  setOpen: any
};

const ProfileMenu: React.FC<Props> = forwardRef<HTMLDivElement, Props>((props, ref) => {
  const { logout } = useAuth();
  const username = "Demo User";

  const toggleVisibility = (e: React.MouseEvent) => {
    e.stopPropagation();
    props.setOpen(!props.isOpen);
  };


  const handleLogout = async () => {
    logout();
  };

  return (
    <div className="items-center md:order-2 space-x-3 md:space-x-0 font-normal text-black">
      <div className="flex flex-row items-center"
        onClick={() => {
          if (props.isOpen) {
            props.setOpen(false);
          }
        }}
      >
        <Avatar
          id="avatarButton"
          name={username}
          url=""
          alt="User"
          onClick={toggleVisibility}
        />
        <div
          className="flex justify-center items-center w-[20px] h-[20px]"
        >
          {props.isOpen ? (
            <svg
              data-accordion-icon
              className="w-2.5 h-2.5 shrink-0 transition-transform duration-200 cursor-pointer"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 10 6"
              onClick={() => props.setOpen(false)}
            >
              <path stroke="#07743f" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M9 5L5 1 1 5" />
            </svg>
          ) : (
            <svg
              data-accordion-icon
              className="w-2.5 h-2.5 rotate-180 shrink-0 transition-transform duration-200 cursor-pointer"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 10 6"
              onClick={() => props.setOpen(true)}
            >
              <path stroke="#07743f" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M9 5L5 1 1 5" />
            </svg>
          )}
        </div>
      </div>

      {props.isOpen && (
        <div
          ref={ref}
          className="w-[250px] absolute mt-[20px] right-0 z-50 text-base list-none bg-white rounded-sm shadow">
          <div className="p-[10px]">
            {/* Arrow (Tail) */}
            <div className="absolute top-[-6px] right-[45px] w-3 h-3 rotate-45 bg-white"></div>
            <ul className="flex flex-col gap-1 text-12" aria-labelledby="user-menu-button">
              <li>
                <Link to="profile" className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700"
                  onClick={() => {
                    props.setOpen(!props.isOpen);
                  }}
                >
                  <ProfileIcon className="w-4 h-4" />
                  <span className="hover:text-[#00AEEF]">Profile</span>
                </Link>
              </li>
              <div className="border-b border-b-[#cccccc] mx-[15px]"></div>
              <li>
                <a
                  onClick={handleLogout}
                  className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 cursor-pointer"
                >
                  <LogOutIcon className="w-4 h-4" />
                  <span className="hover:text-[#00AEEF]">Log Out</span>
                </a>
              </li>
            </ul>
          </div>
          {/* Tenant switcher */}
          <TenantSwitcher />
        </div>
      )}
    </div>
  );
});

export default withClickOutside(ProfileMenu);
