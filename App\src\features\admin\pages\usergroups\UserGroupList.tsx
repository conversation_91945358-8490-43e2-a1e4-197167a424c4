import React from "react";
import { MasterPage } from "../../../../shared/components/render-elements";
import { ColumnSchema } from "../../../../shared/components/common/data-table/types";

const userGroupSchema: ColumnSchema[] = [
  {
    key: "Id",
    title: "Id",
    hidden: true,
    editable: false,
    sorter: false
  },
  {
    key: "Code",
    title: "Code",
    editable: true,
    sorter: true,
    type: "text",
    filterable: true
  },
  {
    key: "Name",
    title: "Name",
    editable: true,
    sorter: true,
    type: "text",
    filterable: true
  },
  {
    key: "Email",
    title: "Email",
    editable: true,
    sorter: true,
    type: "text",
    filterable: true
  }
];

const UserGroupList: React.FC = () => {
  return (
    <MasterPage
      entityName="User Group"
      columnsSchema={userGroupSchema}
      mode="form"
      apiEndpoints={{
        create: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/user-groups`,
        edit: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/user-groups`,
        delete: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/user-groups`,
        list: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/user-groups`
      }}
      routeTemplates={{
        create: "/admin/system/usergroups/create",
        edit: "/admin/system/usergroups/:id/edit",
      }}
    />
  );
};

export default UserGroupList;





























