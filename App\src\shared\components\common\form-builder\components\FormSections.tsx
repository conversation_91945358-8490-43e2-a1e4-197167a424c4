import React, { useState, useEffect } from 'react';
import AddSectionForm from './AddSectionForm';
import { PlusOutlined, EditOutlined, DeleteOutlined, DownOutlined, UpOutlined } from "@ant-design/icons";
import { IFormSection } from "../types";
import { ContextMenu } from '../../context-menu';
import DragDrop from '../../drag-drop/DragDrop';
import styles from "../styles.module.css";
import { handleSectionDelete, handleSubsectionDelete } from '../utils/deletionHandlers';
import TranslationDialog from './TranslationDialog';

interface FormSectionProps {
    sections: IFormSection[];
    setSections: (e: IFormSection[]) => void;
    language: string;
    selectedPath: string;
    setSelectedPath: (e: string) => void;
}

const FormSections: React.FC<FormSectionProps> = ({ sections, setSections, language, setSelectedPath }) => {
    const [showModal, setShowModal] = useState(false);
    const [level, setLevel] = useState(1);
    const [parent, setParent] = useState<string | undefined>(undefined);
    const [selectedSection, setSelectedSection] = useState('');
    const [selectedSubSection, setSelectedSubSection] = useState('');
    const [isTranslationDialogVisible, setIsTranslationDialogVisible] = useState(false);
    const [translationData, setTranslationData] = useState<{ label?: string; languageKey?: string }>({});

    useEffect(() => {
        if (!selectedSection && sections?.length > 0) {
            setSelectedSection(sections[0].key);
            if (sections[0].children?.length > 0) {
                setSelectedSubSection(sections[0].children[0].key);
                setSelectedPath(`${sections[0].key}.${sections[0].children[0].key}`);
            }
        } else if (!selectedSubSection) {
            const subSections = sections.find(x => x.key == selectedSection)?.children;
            if (subSections && subSections.length > 0) {
                setSelectedSubSection(subSections[0].key);
                setSelectedPath(`${selectedSection}.${subSections[0].key}`);
            }
        }

    }, [sections])

    useEffect(() => {
        if (sections.length > 0) {
            setSelectedSection(sections[0].key);
            if (sections[0].children && sections[0].children.length > 0) {
                setSelectedSubSection(sections[0].children[0].key);
                setSelectedPath(`${selectedSection}.${selectedSubSection}`);
            }
        }
    }, []);

    const addSection = (level: number, parent?: string) => {
        setLevel(level);
        setParent(parent);
        setShowModal(true);
    }

    const handleTranslateClick = (label: string | undefined, languageKey: string) => {
        setTranslationData({ label, languageKey });
        setIsTranslationDialogVisible(true);
    };

    const createChildSection = (x: IFormSection, i: number) => {
        return (
            <div
                className={x.key === selectedSubSection ? styles.selectedSubSectionItem : styles.subSectionItem}
                key={`subSection_${x.key}`}
                onClick={() => {
                    setSelectedSubSection(x.key);
                    setSelectedPath(`${selectedSection}.${x.key}`);
                }}
            >
                <span className={styles.subSectionsrNo}><b>{i + 1}</b></span>
                <span className={styles.subSectionText}>{x.title?.[language]}</span>
                <div className='mr-[10px]'>
                    <ContextMenu
                        items={[
                            {
                                key: 'translate',
                                dispayText: 'Translate',
                                icon: EditOutlined,
                                onClick: () => handleTranslateClick(x.title?.[language], language)
                            },
                            {
                                key: 'delete',
                                dispayText: 'Delete',
                                icon: DeleteOutlined,
                                onClick: () => handleSubsectionDelete(sections, setSections, selectedSubSection, selectedSection, setSelectedSubSection, setSelectedPath, x.key, x.title?.[language])

                            }
                        ]}
                    />
                </div>
            </div>
        );
    };

    return (
        <div className='p-5'>
            <div className="h-[62px] flex justify-end text-[14px] font-normal mr-[25px] items-center" onClick={() => addSection(1)}>
                <span>Add New Section</span>
                <span className={styles.addSectionBtn}><PlusOutlined style={{ fontSize: "12px" }} /></span>
            </div>
            <div className="border-t-2 border-gray-300 mr-[15px] w-full" />
            <div className='p-[10px] pt-0'>
                <DragDrop
                    contextId="section"
                    items={sections}
                    onDrop={(newSections) => {
                        setSections(newSections);
                    }}
                    getKey={(item) => item.key}
                    renderItem={(section: IFormSection) => (
                        <div className={styles.sectionContainer} key={`section_${section.key}`}>
                            {/* section title */}
                            <div className={styles.sectionHeader}>
                                <span className={styles.sectionTitle} onClick={() => {
                                    (section.key === selectedSection) ? setSelectedSection('') : setSelectedSection(section.key);
                                }} >
                                    {section.title[language] || language}
                                </span>

                                {/* section context menu */}
                                <ContextMenu items={[{
                                    key: 'translate',
                                    dispayText: 'Translate',
                                    icon: EditOutlined,
                                    onClick: () => handleTranslateClick(section.title?.[language], language)
                                },
                                {
                                    key: 'delete',
                                    dispayText: 'Delete',
                                    icon: DeleteOutlined,
                                    onClick: () => handleSectionDelete(sections, setSections, selectedSection, setSelectedSection, setSelectedSubSection, section.key, section.title?.[language])
                                }
                                ]} />
                                <span className="ml-[7px] mt-[2px]">
                                    {section.key === selectedSection ? (
                                        <UpOutlined onClick={() => setSelectedSection('')} />
                                    ) : (
                                        <DownOutlined onClick={() => setSelectedSection(section.key)} />
                                    )}
                                </span>
                            </div>

                            {(section.key === selectedSection) && (
                                <div className={styles.subSectionContainer}>
                                    {/* sub-sections */}
                                    {section.children && section.children.length > 0 && (
                                        <DragDrop
                                            contextId={`sub-${section.key}`}
                                            items={section.children}
                                            onDrop={(newSubSections) => {
                                                const updatedSections = sections.map(s => {
                                                    if (s.key === section.key) {
                                                        return { ...s, children: newSubSections };
                                                    }
                                                    return s;
                                                });
                                                setSections(updatedSections);
                                            }}
                                            getKey={(item) => item.key}
                                            renderItem={createChildSection}
                                        />
                                    )}
                                    {/* add sub-section button */}
                                    <div className={styles.subSectionItem} onClick={() => addSection(2, section.key)}>
                                        <span className={styles.addSectionBtn}><PlusOutlined style={{ fontSize: "12px" }} /></span>
                                        <span className={styles.subSectionAddText}>{"Add Subsection"}</span>
                                    </div>
                                </div>
                            )}
                        </div>
                    )}
                />
            </div>

            {/* add section dialog */}
            <AddSectionForm
                language={language}
                level={level}
                showModal={showModal}
                onCancel={() => {
                    setLevel(0);
                    setParent(undefined);
                    setShowModal(false);
                }}
                onSubmit={(title: Record<string, string>) => {
                    const arr = [...sections];
                    if (parent) {
                        const index = arr.findIndex(x => x.key == parent);
                        arr[index].children = [...arr[index].children || [], { title, key: (Math.random() + 1).toString(36).substring(7) } as IFormSection]
                    } else {
                        arr.push({ title, key: (Math.random() + 1).toString(36).substring(7) } as IFormSection);
                    }
                    setSections(arr);
                    setLevel(0);
                    setParent(undefined);
                    setShowModal(false);
                }}
            />
            {/* Translation Dialog */}
            <TranslationDialog
                label={translationData.label}
                languageKey={translationData.languageKey}
                visible={isTranslationDialogVisible}
                onClose={() => setIsTranslationDialogVisible(false)}
            />
        </div>
    );
}

export default FormSections;