import apiClient from "../../network/apiClient";

export interface Module {
  ModuleId: number;
  ModuleTeaser: string;
  ModuleName: string;
  ModuleOrder: number;
  ModuleGroupId: number;
  SourceUrl: string | null;
  DeployedGroup: string | null;
  Roles: string | null;
}

export interface ModuleGroup {
  Icon: string;
  Width: number | null;
  Height: number | null;
  Color: string;
  Id: number;
  Title: string;
  Name: string;
  Teaser: string;
  IconOffset: number | null;
  ModuleCaption: string | null;
  GridPosition: number | null;
  ModuleiconPosition: string | null;
  URL: string | null;
  CallBack: string | null;
  IsDisabled: boolean;
  Modules: Module[];
}

export interface MenuItem {
  Id: number;
  Name: string;
  MenuLink: string;
  Action: string;
  URL: string;
  Controller: string;
  Area: string;
  ParentId: number | null;
  SortOrder: number;
  ModuleId: number;
  ModuleGroupId: number;
  Children: MenuItem[];
  Color: string;
  ParentName: string;
  File: string;
  BaseUrl: string;
  ControllerType: string;
  SourceModuleId: number | null;
  Module: string;
  ModuleTeaser: string;
  Service: string;
  ScreenLayout: string;
  MenuParamJson: any;
  IconColor: string;
  MenuIcon: string;
}

export interface TenantItem {
  DisplayText: string;
  Value: string | number;
}

export interface MenuResult {
  ModuleGroup: ModuleGroup[];
  Modules: Module[];
  Menu: MenuItem[];
  Tenants: TenantItem[];
}

const BASE_URL = `${import.meta.env.VITE_APP_API_BASE_URL}/api/application-settings`;

export const appSettingService = {
  // GET - Fetch all users
  async fetchMenu(): Promise<MenuResult | null> {
    try {
      const response = await apiClient.get<MenuResult>(`${BASE_URL}/menus`);
      return response.data ?? null;
    } catch (error) {
      console.error("Error fetching users:", error);
      return null;
    }
  },
};
