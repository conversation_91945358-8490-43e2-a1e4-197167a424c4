import apiClient from "../../network/apiClient";

export interface Permission {
  Id: number;
  BusinessUnitId: string;
  ModuleId: string;
  RoleId: string;
}

export interface OptionItem {
  DisplayText: string;
  Value: string | number;
}

interface FetchResult {
  Record: Permission[];
  PermissionRights?: string;
  BusinessUnitId: OptionItem[];
  ModuleId: OptionItem[];
  RoleId: OptionItem[];
}

const BASE_URL = `${import.meta.env.VITE_Admin_API_BASE_URL}/api/permission`;

export const permissionService = {
  async fetchAll(): Promise<FetchResult> {
    try {
      const response = await apiClient.get<FetchResult>(BASE_URL);
      return response.data;
    } catch (error) {
      console.error("Error fetching permissions:", error);
      throw error;
    }
  },

  async getById(id: number): Promise<Permission> {
    try {
      const response = await apiClient.get<{ Record: Permission }>(`${BASE_URL}/${id}`);
      return response.data.Record;
    } catch (error) {
      console.error(`Error fetching permission with ID ${id}:`, error);
      throw error;
    }
  },

  async create(permission: Partial<Permission>): Promise<Permission | null> {
    try {
      const response = await apiClient.post<Permission>(BASE_URL, permission);
      return response.data ?? null;
    } catch (error) {
      console.error("Error creating permission:", error);
      return null;
    }
  },

  async update(id: number, updatedPermission: Partial<Permission>): Promise<Permission | null> {
    try {
      const response = await apiClient.put<Permission>(`${BASE_URL}/${id}`, updatedPermission);
      return response.data ?? null;
    } catch (error) {
      console.error("Error updating permission:", error);
      return null;
    }
  },

  async delete(id: number): Promise<boolean> {
    try {
      await apiClient.delete(`${BASE_URL}/${id}`);
      return true;
    } catch (error) {
      console.error("Error deleting permission:", error);
      return false;
    }
  }
};
