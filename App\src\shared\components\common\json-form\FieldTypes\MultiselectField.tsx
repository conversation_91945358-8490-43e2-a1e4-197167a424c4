import React, { useState, useEffect, useRef } from "react";
import { FormField } from "../types";
import styles from "./styles.module.css";

interface MultiSelectFieldProps {
  field: FormField;
  value: (string | number)[];
  onChange: (name: string, value: (string | number)[]) => void;
}

const MultiSelectField: React.FC<MultiSelectFieldProps> = ({ field, value, onChange }) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [showDropdown, setShowDropdown] = useState(false);
  const [visibleChipCount, setVisibleChipCount] = useState(value.length);
  const multiSelectRef = useRef<HTMLDivElement>(null);
  const chipContainerRef = useRef<HTMLDivElement>(null);

  const filteredOptions = Array.isArray(field.options)
    ? field.options.filter((option) => option.label.toLowerCase().includes(searchQuery.toLowerCase()))
    : [];

  const handleSelect = (selectedValue: string | number) => {
    const newValue = value.includes(selectedValue)
      ? value.filter((v) => v !== selectedValue) // Remove value
      : [...value, selectedValue]; // Add value
    onChange(field.name, newValue);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (multiSelectRef.current && !multiSelectRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if (chipContainerRef.current) {
      const containerWidth = chipContainerRef.current.offsetWidth;
      let accumulatedWidth = 0;
      let count = 0;

      chipContainerRef.current.childNodes.forEach((node) => {
        if (node instanceof HTMLElement) {
          accumulatedWidth += node.offsetWidth + 8; // Add margin between chips
          if (accumulatedWidth < containerWidth) {
            count++;
          }
        }
      });

      setVisibleChipCount(Math.max(1, count - 1));
    }
  }, [value]);

  return (
    <div className={styles.multiSelectField} ref={multiSelectRef}>
      <div className={styles.inputContainer} onClick={() => setShowDropdown(true)}>
        {/* Selected Chips */}
        <div className={styles.selectedChips} ref={chipContainerRef}>
          {value.slice(0, visibleChipCount).map((selected) => {
            const option = Array.isArray(field.options) && field.options.find((opt) => opt.value === selected);
            return option ? (
              <span key={selected} className={styles.chip}>
                {option.label} <button onClick={() => handleSelect(selected)}>×</button>
              </span>
            ) : null;
          })}
          {value.length > visibleChipCount && (
            <span className={styles.moreChip}>
              +{value.length - visibleChipCount}
            </span>
          )}
        </div>
        <input
          className="border-0 focus:ring-0 focus:outline-none"
          type="text"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onFocus={() => setShowDropdown(true)}
        />
        {/* Dropdown Icon */}
        <span className={styles.dropdownIcon}>
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#555" strokeWidth="2">
            <polyline points="6 9 12 15 18 9"></polyline>
          </svg>
        </span>
      </div>
      {showDropdown && (
        <ul className={styles.suggestionsList}>
          {filteredOptions.map((option) => (
            <li key={option.value} onClick={() => handleSelect(option.value)}>
              <input type="checkbox" checked={value.includes(option.value)} readOnly /> {option.label}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default MultiSelectField;
