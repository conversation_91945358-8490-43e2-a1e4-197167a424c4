import React, { useState } from 'react';
import { Input } from 'antd';
import { PlusSquareOutlined, DeleteOutlined } from '@ant-design/icons';
import { IFormElement } from '../types';

interface SelectFieldProps {
    element: IFormElement;
    setElements: (e: IFormElement[]) => void;
    elements: IFormElement[];
}

const SelectField: React.FC<SelectFieldProps> = ({ element, setElements, elements }) => {
    const [choices, setChoices] = useState<{ value: string; label: string }[]>(element.options || [{ value: '', label: '' }]);

    const updateElementOptions = (updatedChoices: { value: string; label: string }[]) => {
        const index = elements.findIndex((el) => el.slug === element.slug);
        if (index >= 0) {
            const updatedElements = [...elements];
            updatedElements[index] = {
                ...updatedElements[index],
                options: updatedChoices
            };
            setElements(updatedElements);
        }
    };

    const handleValueChange = (index: number, value: string) => {
        const updated = [...choices];
        updated[index].value = value;
        setChoices(updated);
        updateElementOptions(updated);
    };

    const handleLabelChange = (index: number, label: string) => {
        const updated = [...choices];
        updated[index].label = label;
        setChoices(updated);
        updateElementOptions(updated);
    };

    const addChoice = () => {
        const updated = [...choices, { value: '', label: '' }];
        setChoices(updated);
        updateElementOptions(updated);
    };

    const removeChoice = (index: number) => {
        const updated = choices.filter((_, i) => i !== index);
        setChoices(updated);
        updateElementOptions(updated);
    };

    return (
        (element.optionsFromURL) ? <div className="space-y-4 bg-[#F5F5F5] p-5 rounded">
            <div className="flex mb-[10px] text-[14px] text-[#0B3456] font-medium">
                <label className="w-[50%]">Option from API URL</label>
            </div>
            <div className="flex items-start space-x-3">
                <Input
                    placeholder="Enter URL"
                    value={element.optionsFromURL}
                    disabled
                />
            </div>
        </div> : <div className="space-y-4 bg-[#F5F5F5] p-5 rounded">
            <div className="flex mb-[10px] text-[14px] text-[#0B3456] font-medium">
                <label className="w-[50%]">Value</label>
                <label className="w-[50%]">Label</label>
            </div>
            {choices.map((choice, index) => (
                <div key={index} className="flex items-start space-x-3">
                    <div className="w-[50%]">
                        <Input
                            placeholder="Enter Value"
                            value={choice.value}
                            onChange={(e) => handleValueChange(index, e.target.value)}
                        />
                    </div>
                    <div className="w-[50%]">
                        <Input
                            placeholder="Enter Label"
                            value={choice.label}
                            onChange={(e) => handleLabelChange(index, e.target.value)}
                        />
                    </div>
                    <div
                        onClick={() => removeChoice(index)}
                        className={`mt-[5px] ${choices.length === 1 ? 'text-[#B0B0B0] cursor-not-allowed' : 'text-[#0B3456] cursor-pointer'}`}
                    >
                        <DeleteOutlined />
                    </div>
                </div>
            ))}
            <div className="flex items-center mt-4 text-[#00AEEF] hover:text-[#0B3456] text-[16px] leading-0 cursor-pointer" onClick={addChoice}>
                <span className="mr-2">
                    <PlusSquareOutlined />
                </span>
                Add Choice
            </div>
        </div>
    );
};

export default SelectField;