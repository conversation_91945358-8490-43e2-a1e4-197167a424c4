import React from 'react';
import { ArrowLeftOutlined, ArrowRightOutlined } from "@ant-design/icons";

import styles from "../styles.module.css";

interface NavigationButtonsProps {
    selectedSection: string;
    selectedSubSection: string;
    setSelectedSubSection: (key: string) => void;
    sections: any[];
}

const NavigationButtons: React.FC<NavigationButtonsProps> = ({ selectedSection, selectedSubSection, setSelectedSubSection, sections }) => {

    const currentSubSections = sections.find(sec => sec.key === selectedSection)?.children || [];
    const currentIndex = currentSubSections.findIndex((sub: any) => sub.key === selectedSubSection);

    const goToPrevious = () => {
        if (currentIndex > 0) {
            setSelectedSubSection(currentSubSections[currentIndex - 1].key);
        }
    };

    const goToNext = () => {
        if (currentIndex < currentSubSections.length - 1) {
            setSelectedSubSection(currentSubSections[currentIndex + 1].key);
        }
    };

    return <div className='flex items-center justify-start w-[250px] h-[60px]'>
        <button type='button' className={`ml-[20px] ${styles.navigationButton}`} onClick={goToPrevious} disabled={currentIndex === 0}>
            <span className={styles.leftArrow}><ArrowLeftOutlined /></span>
            <span className={styles.navigationBtnText}>PREVIOUS</span>
        </button>
        <button type='button' className={`ml-[20px] ${styles.navigationButton}`} onClick={goToNext} disabled={currentIndex === currentSubSections.length - 1}>
            <span className={styles.navigationBtnText}>NEXT</span>
            <span className={styles.rightArrow}><ArrowRightOutlined /></span>
        </button>
    </div>;
}

export default NavigationButtons;