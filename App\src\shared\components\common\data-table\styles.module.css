.dataTable {
  background-color: #fff;
  border: 1px solid #dbdbdb;
  border-radius: 6px;
  padding: 10px;
}

.dataTable th {
  background-color: #fff !important;
  padding: 4px !important;
  /* height: 32px; */
}

.headerRow {
  color: #000000e0;
  font-weight: 600;
}

.dataTable tr td {
  border: none !important;
  padding: 4px !important;
}

.dataTable tr td input {
  padding: 2px 4px !important;
  background-color: #fff;
}

.dataTable tr td select {
  padding: 3px 4px !important;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 3px;
}

.dataTable tr:hover {
  background-color: #e1ebf0 !important;
}

.dataTable tr:nth-child(odd) {
  background-color: #f7f7f7;
}

.nameColumnCell {
  font-weight: bold;
  color: #1890ff;
}

.paginationContainer {
  justify-content: flex-end;
  margin-top: 20px;
}
