import { useEffect, useState } from "react";

export const useSorting = <T,>(initialData: T[]) => {
  const [sortedData, setSortedData] = useState(initialData);
  const [sortConfig, setSortConfig] = useState<{ key: keyof T; order: "asc" | "desc" } | null>(null);

  useEffect(() => {
    setSortedData(initialData || []);
  }, [initialData]);

  const handleSort = (key: keyof T) => {
    const newOrder = sortConfig?.order === "asc" ? "desc" : "asc"; // Toggle sorting order

    const sorted = [...sortedData].sort((a, b) => {
      if (typeof a[key] === "string" && typeof b[key] === "string") {
        return newOrder === "asc" ? a[key].localeCompare(b[key]) : b[key].localeCompare(a[key]);
      }
      if (typeof a[key] === "number" && typeof b[key] === "number") {
        return newOrder === "asc" ? a[key] - b[key] : b[key] - a[key];
      }
      return 0;
    });

    setSortedData(sorted);
    setSortConfig({ key, order: newOrder });
  };

  return { sortedData, sortConfig, handleSort };
};
