import apiClient from "../../network/apiClient";

// Define BusinessUnit structure
export interface BusinessUnit {
  Id: number;
  Code: string;
  Name: string;
  OBSSettingId: number;
  OBSSetting?: any;
  Address?: string;
  PinCode?: string;
  Phone?: string;
  Fax?: string;
  Email?: string;
  WebSite?: string;
  ParentBusinessUnitId?: number;
  AccOBSBunitId?: number;
  Tenant?: number;
  Active: number;
  Remarks?: string;
  Level: number;
  Hierarchy?: string;
  GeoCoordinates?: string;
  SettingDetail?: string;
  UpdatedDate?: string;
  Logo?: string;
}

// Define fetch-all response structure
interface FetchResult {
  Record: BusinessUnit[];
  UserRights: string;
}

const BASE_URL = `${import.meta.env.VITE_Admin_API_BASE_URL}/api/business-units`;

export const businessUnitService = {
  // GET - Fetch all business units
  async fetchAll(): Promise<FetchResult | null> {
    try {
      const response = await apiClient.get<FetchResult>(BASE_URL);
      return response.data ?? null;
    } catch (error) {
      console.error("Error fetching business units:", error);
      return null;
    }
  },

  // GET - Fetch a single business unit by ID
  async getById(id: number): Promise<BusinessUnit | null> {
    try {
      const response = await apiClient.get<{ Record: BusinessUnit }>(`${BASE_URL}/${id}`);
      return response.data.Record ?? null;
    } catch (error) {
      console.error(`Error fetching business unit with ID ${id}:`, error);
      throw error;
    }
  },

  // POST - Create a new business unit
  async create(data: Partial<BusinessUnit>): Promise<BusinessUnit | null> {
    try {
      const response = await apiClient.post<BusinessUnit>(BASE_URL, data);
      return response.data ?? null;
    } catch (error) {
      console.error("Error creating business unit:", error);
      return null;
    }
  },

  // PUT - Update an existing business unit
  async update(id: number, data: Partial<BusinessUnit>): Promise<BusinessUnit | null> {
    try {
      const response = await apiClient.put<BusinessUnit>(`${BASE_URL}/${id}`, data);
      return response.data ?? null;
    } catch (error) {
      console.error(`Error updating business unit with ID ${id}:`, error);
      return null;
    }
  },

  // DELETE - Remove a business unit
  async delete(id: number): Promise<boolean> {
    try {
      await apiClient.delete(`${BASE_URL}/${id}`);
      return true;
    } catch (error) {
      console.error(`Error deleting business unit with ID ${id}:`, error);
      return false;
    }
  }
};
