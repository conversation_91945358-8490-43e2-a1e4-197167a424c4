.contextMenuIcon {
    background-image: url('/images/button/more-small-20x20.svg') !important;
    background-repeat: no-repeat;
    width: 22px;
    height: 22px;
    display: flex;
    background-color: transparent !important;
    margin-top: 5px;
    position: 'relative';
    cursor: 'pointer';
}
.contextMenuIcon:hover
{
  background-image: url('/images/button/more-small-hover-20x20.svg') !important;
  background-repeat: no-repeat;
  width: 22px;
  height: 22px;
  display: block;
}

.contextMenuBody {
    position: 'absolute';
    background-color: 'white';
    border: '1px solid #ccc';
    box-shadow: '0 4px 8px rgba(0,0,0,0.1)';
    border-radius: '4px';
    padding: '10px';
}

.contextMenuItemText {
    font-size: 13px;
    color: #4d4d4d;
    font-weight: 500;
    padding-left: 8px;
}

.contextMenuItem {
    display: "flex";
    align-items: "center";
    border-bottom: 2px solid #ccc;
    padding-bottom: 7px;
}