import apiClient from "../../network/apiClient";

interface Role {
  Id: number;
  Code: string;
  Name: string;
  Level: number;
  IsSystemDefined?: boolean;
  CreatedDate?: string | null;
  UpdatedDate?: string | null;
}

// Define fetch-all response structure
interface FetchResult {
  Record: Role[];
  UserRights: string;
}

const BASE_URL = `${import.meta.env.VITE_Admin_API_BASE_URL}/api/roles`;

export const roleService = {
  /**
   * GET - Fetch all roles.
   */
  async fetchAll(): Promise<FetchResult> {
    try {
      const response = await apiClient.get<FetchResult>(BASE_URL);
      return response.data;
    } catch (error) {
      console.error("Error fetching roles:", error);
      throw error;
    }
  },

  /**
   * GET - Fetch a single role by its ID.
   * @param roleId - The ID of the role to fetch.
   */
  async getById(roleId: number): Promise<Role> {
    try {
      const response = await apiClient.get<Role>(`${BASE_URL}/${roleId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching role with ID ${roleId}:`, error);
      throw error;
    }
  },

  /**
   * POST - Create a new role.
   * @param role - The role data to create.
   */
  async create(role: Role): Promise<Role> {
    try {
      const response = await apiClient.post<Role>(BASE_URL, role);
      return response.data;
    } catch (error) {
      console.error("Error creating role:", error);
      throw error;
    }
  },

  /**
   * PUT - Update an existing role.
   * @param roleId - The ID of the role to update.
   * @param updatedRole - The updated role fields.
   */
  async update(roleId: number, updatedRole: Partial<Role>): Promise<Role> {
    try {
      const response = await apiClient.put<Role>(`${BASE_URL}/${roleId}`, updatedRole);
      return response.data;
    } catch (error) {
      console.error(`Error updating role with ID ${roleId}:`, error);
      throw error;
    }
  },

  /**
   * DELETE - Remove a role by its ID.
   * @param roleId - The ID of the role to delete.
   */
  async delete(roleId: number): Promise<boolean> {
    try {
      await apiClient.delete(`${BASE_URL}/${roleId}`);
      return true;
    } catch (error) {
      console.error(`Error deleting role with ID ${roleId}:`, error);
      return false;
    }
  }
};
