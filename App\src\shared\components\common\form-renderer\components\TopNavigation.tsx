import React from 'react';

import styles from "../styles.module.css";

interface TopNavigationProps {
    selectedSection: string;
    setSelectedSection: (key: string) => void;
    sections: any[];
}

const TopNavigation: React.FC<TopNavigationProps> = ({
    selectedSection,
    setSelectedSection,
    sections
}) => {

    return <div className={`w-full flex ${styles.topNavigationSection}`}>
        { sections.map((section, i) => {
            return <div 
            onClick={() => {setSelectedSection(section.key)}}
            key={`section_${i}`} 
            className={`w-full flex ${styles.topMenuTab} ${selectedSection == section.key ? styles.topMenuTabActive: ''}`}>
                {section.title}
                <span className={selectedSection == section.key ?`${styles.topMenuActiveTabArrow}`:""}/>
            </div>;
        }) }
    </div>;
}

export default TopNavigation;