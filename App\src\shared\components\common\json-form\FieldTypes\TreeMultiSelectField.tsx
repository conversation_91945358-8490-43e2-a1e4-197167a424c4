import React, { useState, useEffect, useRef } from "react";
import { FormField, FormOption } from "../types";
import styles from "./styles.module.css";

interface TreeMultiSelectFieldProps {
  field: FormField;
  value: (string | number)[];
  onChange: (name: string, value: (string | number)[]) => void;
}

const TreeMultiSelectField: React.FC<TreeMultiSelectFieldProps> = ({ field, value, onChange }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [expandedNodes, setExpandedNodes] = useState<Set<string | number>>(new Set());
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const toggleDropdown = () => setIsOpen((prev) => !prev);
  const toggleExpand = (nodeValue: string | number) =>
    setExpandedNodes((prev) => new Set(prev.has(nodeValue) ? [...prev].filter((v) => v !== nodeValue) : [...prev, nodeValue]));

  const handleSelect = (node: FormOption) => {
    let newValues = value.includes(node.value) ? value.filter((v) => v !== node.value) : [...value, node.value];

    if (node.children) {
      const childValues = node.children.map((child) => child.value);
      newValues = newValues.includes(node.value) ? [...newValues, ...childValues] : newValues.filter((v) => !childValues.includes(v));
    }

    onChange(field.name, newValues);
  };

  const renderTreeNodes = (nodes: FormOption[]) =>
    nodes.map((node) => (
      <div key={node.value} className={styles.treeNode}>
        <div className={styles.treeItem}>
          {node.children && (
            <span onClick={() => toggleExpand(node.value)} className={styles.toggleIcon}>
              {expandedNodes.has(node.value) ? (
                <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <polyline points="6 9 12 15 18 9" />
                </svg> // Downward arrow when expanded
              ) : (
                <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <polyline points="9 6 15 12 9 18" />
                </svg> // Rightward arrow when collapsed
              )}
            </span>
          )}
          <input type="checkbox" checked={value.includes(node.value)} onChange={() => handleSelect(node)} />
          <span className={styles.treeLabel} onClick={() => handleSelect(node)}>{node.label}</span>
        </div>
        {node.children && expandedNodes.has(node.value) && <div className={styles.treeChildren}>{renderTreeNodes(node.children)}</div>}
      </div>
    ));

  // Generate display text from selected values
  const selectedLabels = (Array.isArray(field.options) ? field.options : [])
    ?.flatMap((node) => [node, ...(node.children || [])])
    .filter((node) => value.includes(node.value))
    .map((node) => node.label)
    .join(", ");

  return (
    <div className={styles.treeDropdown} ref={dropdownRef}>
      <div className={styles.treeDropdownSelected} tabIndex={0} onClick={toggleDropdown}>
        {selectedLabels || `Select ${field.label}`}
        {/* Dropdown Icon */}
        <span className={styles.dropdownIcon}>
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#555" strokeWidth="2">
            <polyline points="6 9 12 15 18 9"></polyline>
          </svg>
        </span>

      </div>
      {isOpen && <div className={styles.treeDropdownMenu}>{renderTreeNodes(Array.isArray(field.options) ? field.options : [])}</div>}
    </div>
  );
};

export default TreeMultiSelectField;
