/* Popup Container */
.container {
    position: relative;
  }
  
  .icon {
    font-size: 24px;
    cursor: pointer;
  }
  
  .popup {
    position: absolute;
    top: 30px;
    right: 0;
    width: 300px;
    padding: 20px;
    background-color: white;
    border: 1px solid #ddd;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: flex;
    flex-direction: column;
  }
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
  }
  
  .status {
    margin-top: 10px;
  }
  
  .statusValue {
    font-weight: bold;
  }
  
  .chatList {
    max-height: 150px;
    min-height: 50px; /* <- add this */
    overflow-y: auto;
    margin-bottom: 12px;
  }  
  
  .chatItem {
    margin-bottom: 8px;
  }
  
  .noComments {
    color: #888;
  }
  
  .inputArea {
    margin-top: 10px;
    display: flex;
    flex-direction: column;
  }
  
  .textarea {
    width: 100%;
    height: 80px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    resize: none;
  }
  
  .buttonGroup {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 10px;
  }
  
  .resolveButton {
    background-color: #4CAF50;
    color: white;
  }
  