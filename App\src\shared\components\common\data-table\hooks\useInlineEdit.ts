import { useEffect, useState } from "react";

export const useInlineEdit = <T extends { key: string }>(initialData: T[]) => {
  const [data, setData] = useState(initialData);
  const [editingKey, setEditingKey] = useState<string | null>(null);
  const [editValues, setEditValues] = useState<Partial<T>>({});

  useEffect(() => {
    setData(initialData || []);
  }, [initialData]);

  const startEdit = (key: string) => {
    setEditingKey(key);
    const item = data.find((i) => i.key === key);
    if (item) setEditValues({ ...item });
  };

  const saveEdit = (key: string) => {
    setData(data.map((item) => (item.key === key ? { ...item, ...editValues } : item)));
    setEditingKey(null);
  };

  const cancelEdit = () => setEditingKey(null);

  const handleChange = (field: keyof T, value: any) => {
    setEditValues((prev) => ({ ...prev, [field]: value }));
  };

  const deleteItem = (key: string) => {
    setData(data.filter((item) => item.key !== key));
  };

  return { data, editingKey, editValues, startEdit, saveEdit, cancelEdit, handleChange, deleteItem };
};
