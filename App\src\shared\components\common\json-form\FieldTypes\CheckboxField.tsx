import React from "react";
import { FormField } from "../types";

interface CheckboxFieldProps {
  field: FormField; // Field schema
  value: boolean | string[]; // Boolean for single checkbox or string[] for checkbox group
  onChange: (name: string, value: any) => void; // Change handler
}

const CheckboxField: React.FC<CheckboxFieldProps> = ({ field, value, onChange }) => {
  const isGroup = Array.isArray(field.options) && field.options.length > 0;

  const handleCheckboxChange = (optionValue: string, checked: boolean) => {
    // For checkbox group: update the array of selected values
    const updatedValue = checked
      ? [...(Array.isArray(value) ? value : []), optionValue] // Safely handle as an array
      : (Array.isArray(value) ? value : []).filter((v) => v !== optionValue); // Filter only if it's an array

    onChange(field.name, updatedValue);
  };

  if (isGroup) {
    // Render checkbox group
    return (
      <div>
        <label className="checkbox-group-label">{field.label}</label>
        <div className="checkbox-group">
          {Array.isArray(field.options) && field.options?.map((option) => (
            <div key={option.value} className="checkbox-item">
              <input
                type="checkbox"
                id={`${field.name}-${option.value}`}
                name={field.name}
                checked={(value as string[]).includes(String(option.value))}
                onChange={(e) => handleCheckboxChange(String(option.value), e.target.checked)}
              />
              <label htmlFor={`${field.name}-${option.value}`}>{option.label}</label>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Render single checkbox
  return (
    <div>
      <input
        type="checkbox"
        id={field.name}
        name={field.name}
        checked={!!value}
        onChange={(e) => onChange(field.name, e.target.checked)}
      />
      <label htmlFor={field.name}>{field.optionText || field.label}</label>
    </div>
  );
};

export default CheckboxField;
