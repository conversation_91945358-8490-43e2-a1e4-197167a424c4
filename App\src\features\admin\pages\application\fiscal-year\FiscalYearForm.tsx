import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { JF<PERSON>, JFormSchema } from "../../../../../shared/components/common/json-form";
import { message } from "antd";
import { Loader } from "../../../../../shared/components/common";
import { FiscalYear, fiscalYearService } from "../../../../../api/services/admin/fiscalYearService";

const FiscalYearForm: React.FC<{ mode: "create" | "edit" }> = ({ mode }) => {
  const navigate = useNavigate();
  const { id } = useParams(); // Get fiscal year ID from URL params for edit mode
  const [formData, setFormData] = useState<FiscalYear | null>(null);
  const fiscalYearId = Number(id);

  const schema: JFormSchema = {
    title: `${mode} Fiscal Year`,
    formGroups: [
      {
        title: "",
        columns: 4,
        fields: [
          {
            name: "Id",
            type: "hidden",
            required: true,
            defaultValue: 0
          },
          {
            name: "FiscalYearCode",
            type: "text",
            label: "FiscalYearCode",
            required: true,
            validation: {
              required: true,
              maxLength: 5
            }
          },
          {
            name: "StartDate",
            type: "date",
            label: "StartDate",
            required: true
          },
          {
            name: "EndDate",
            type: "date",
            label: "EndDate",
            required: true
          },
          {
            name: "Active",
            type: "toggle",
            label: "Active",
            required: true,
            defaultValue: 1,
            transform: (value) => (value ? 1 : 0),
            validation: {
              required: true,
              custom: (value) => {
                const num = Number(value)
                return num === 0 || num === 1 ? undefined : "Value must be 0 or 1";
              },
            }
          },
        ]
      }
    ]
  };

  useEffect(() => {
        if (mode === "edit" && fiscalYearId) {
          fiscalYearService.getById(fiscalYearId)
            .then(setFormData)
            .catch((error) => console.error("Error fetching fiscalyear data:", error));
        }
      }, [mode, fiscalYearId]);
     
  const handleSubmit = async (formValues: Record<string, any>) => {
    try {
      const response = mode === "edit" && fiscalYearId
        ? await fiscalYearService.update(fiscalYearId, formValues)
        : await fiscalYearService.create(formValues);
  
      if (response) {
        message.success(`Fiscal Year ${mode === "edit" ? "updated" : "created"} successfully.`);
        navigate("..");
      } else {
        message.error(`Failed to ${mode === "edit" ? "update" : "create"} fiscal year. Please try again.`);
      }
    } catch (error: any) {
      console.error("Error submitting form:", error);
      message.error(error?.response?.data?.message || "An unexpected error occurred.");
    }
  };

  return (
    <div className="flex flex-col gap-2 bg-white p-[30px] rounded-[6px]">
      {mode === "edit" && !formData ? (
        <Loader />
      ) : (
        <JForm schema={schema} onSubmit={handleSubmit} data={formData || {}} />
      )}
    </div>
  );
};

export default FiscalYearForm;
