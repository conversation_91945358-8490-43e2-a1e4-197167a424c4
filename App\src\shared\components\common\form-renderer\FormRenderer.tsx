import React, { useState, useEffect } from 'react';
import TopNavigation from './components/TopNavigation';
import LeftNavigation from './components/LeftNavigation';
import FormContainer from './components/FormContainer';
import NavigationButtons from './components/NavigationButtons';

import styles from "./styles.module.css";

interface FormRendererProps {
    schema: Record<string, any>;
    datum: Record<string, any>;
    setDatum: (e: Record<string, any>) => void;
    onSave: () => void;
}

const FormRenderer: React.FC<FormRendererProps> = ({ schema, datum, setDatum, onSave }) => {

    const [selectedSection, setSelectedSection] = useState('');
    const [selectedSubSection, setSelectedSubSection] = useState('');
    const [formSchema, setFormSchema] = useState(null);
    const [sections, setSections] = useState<any[]>([]);
    const [menuOpen, setMenuOpen] = useState(true);
    const language = "en";

    useEffect(() => {
        setFormSchema(sections.find(x => x.key === selectedSection)?.children?.find((x: any) => x.key === selectedSubSection));
    }, [selectedSubSection]);

    useEffect(() => {
        setSections(schema[language]?.sections);
        (schema[language]?.sections.length > 0) && setSelectedSection(schema[language]?.sections[0].key);
        (schema[language]?.sections.length > 0 && schema[language]?.sections[0].children.length > 0) && setSelectedSubSection(schema[language]?.sections[0].children[0].key);
    }, [schema]);

    if (!sections || sections.length == 0)
        return <></>;

    return <div>
        {/* top navigation menu */}
        <TopNavigation sections={sections} selectedSection={selectedSection} setSelectedSection={setSelectedSection} />
        <div className='w-[100%] mt-[10px] flex'>
            {
                (sections.find(x => x.key === selectedSection)?.children?.length > 0) && <div className={`${menuOpen ? 'w-[278px]' : 'w-[50px]'} relative flex`}>
                    {/* left navigation section */}
                    <LeftNavigation
                        selectedSubSection={selectedSubSection}
                        setSelectedSubSection={setSelectedSubSection}
                        subSections={sections.find(x => x.key === selectedSection).children}
                    />
                    <div className={`w-[18px] cursor-pointer absolute ${menuOpen ? 'left-[270px]' : 'left-[45px]'}`}>
                        <button className={menuOpen ? styles.slideCollapse : styles.slideExpand} onClick={() => setMenuOpen(!menuOpen)}></button>
                    </div>
                </div>
            }
            { /* form element container */}
            <div className={`${(sections.find(x => x.key === selectedSection)?.children?.length > 0) ? (menuOpen ? 'w-[calc(100%-290px)]' : 'w-[calc(100%-60px)]') : 'w-[100%]'} h-[calc(72vh)-10px] ${(sections.find(x => x.key === selectedSection)?.children?.length > 0) ? 'ml-[20px]' : ''}`}>
                {/* form renderer */}
                <div className='w-[100%] h-[calc(72vh-73px)] rounded-[6px] overflow-auto bg-white'>
                    {
                       formSchema && <FormContainer formSchema={formSchema} formData={datum} setFormData={setDatum} />
                    }
                </div>
                {/*button pannel  */}
                <div className='bg-white h-[60px] w-[100%] mt-[10px] rounded-[6px] flex'>
                    <NavigationButtons
                        selectedSection={selectedSection}
                        selectedSubSection={selectedSubSection}
                        setSelectedSubSection={setSelectedSubSection}
                        sections={sections}
                    />
                    <div className='flex items-center justify-end w-[calc(100%-300px)] h-[60px]'>
                        <button type='button' className={`ml-[20px] ${styles.navigationButton}`} onClick={onSave} >
                            <span className={styles.navigationBtnText}>Save</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>;
}

export default FormRenderer;