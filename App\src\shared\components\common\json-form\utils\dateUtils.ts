class DateUtils {
  /** Parse a date string into a valid Date object */
  static parseDate(dateString: string): Date | null {
    if (!dateString) return null;
    const parsedDate = new Date(dateString);
    return isNaN(parsedDate.getTime()) ? null : parsedDate;
  }

  /** Format date based on user-defined format strings */
  static formatDate(dateString: string, format: string, locale: string = "en-US"): string {
    const date = DateUtils.parseDate(dateString);
    if (!date) return "";

    const formatMap: { [key: string]: string } = {
      YYYY: date.getFullYear().toString(),
      MM: String(date.getMonth() + 1).padStart(2, "0"),
      DD: String(date.getDate()).padStart(2, "0"),
      HH: String(date.getHours()).padStart(2, "0"),
      mm: String(date.getMinutes()).padStart(2, "0"),
      ss: String(date.getSeconds()).padStart(2, "0"),
      dddd: date.toLocaleDateString(locale, { weekday: "long" }),
      ddd: date.toLocaleDateString(locale, { weekday: "short" }),
      A: date.getHours() >= 12 ? "PM" : "AM",
    };

    return format.replace(/YYYY|MM|DD|HH|mm|ss|dddd|ddd|A/g, (match) => formatMap[match] || match);
  }
}

export default DateUtils;
