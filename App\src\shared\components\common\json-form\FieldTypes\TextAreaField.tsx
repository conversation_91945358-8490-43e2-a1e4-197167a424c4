import React from "react";
import { FormField } from "../types";

interface TextAreaFieldProps {
  field: FormField;
  value: string;
  onChange: (name: string, value: string) => void;
}

const TextAreaField: React.FC<TextAreaFieldProps> = ({ field, value, onChange }) => {
  return (
    <div>
      <textarea
        name={field.name}
        required={field.required}
        value={value}
        onChange={(e) => onChange(field.name, e.target.value)}
      />
    </div>
  );
};

export default TextAreaField;
