import React, { useEffect, useState } from "react";
import { useLocation, Outlet, useMatches, NavLink } from "react-router-dom";
import styles from "../../../../styles/ModuleLayout.module.css";
import { useMenu } from "../../../../providers/MenuProvider";
import { MenuItem } from "../../../../api/services/common/appSettingService";

interface MenuPanelHandle {
  menuPanel?: boolean;
}

const ModuleLayout: React.FC = () => {
  const location = useLocation();
  const matches = useMatches();
  const data = useMenu();

  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);

  // Extract menuPanel from the last matched route
  const currentHandle = matches[matches.length - 1]?.handle as MenuPanelHandle;
  const menuPanel = currentHandle?.menuPanel ?? true; // Default to true if not provided

  // Extract route segments from the pathname
  const pathSegments = location.pathname.split("/").filter(Boolean); // Removes empty values

  // Assuming URL structure is `/moduleGroup/module`
  const module = pathSegments[1]; // Second segment

  useEffect(() => {
    if (menuPanel) {
      const modules = data?.Menu?.filter((item) => item?.Module.toLowerCase() === module);
      const allChildren = modules?.flatMap(item => item.Children || []); // Combine all children into a single array
      setMenuItems(allChildren ?? []);
    }
  }, [data, menuPanel]);

  return (
    <div className={styles.panelContainer}>
      {menuPanel && ( // Conditionally render the menu panel
        <div className={styles.leftPanel}>
          <ul className={styles.navList}>
            {menuItems.map((item, index) => {
              return (
                <NavLink
                  key={item.Id}
                  to={item.URL}
                  className={({ isActive }) => `${styles.navItem} ${isActive ? styles.activeNavItem : ""}`}
                >
                  <span className={styles.indexBox}>{index + 1}</span>
                  <span>{item.Name}</span>
                </NavLink>
              );
            })}
          </ul>
        </div>
      )}

      <div style={{ flex: 1 }}>
        <Outlet />
      </div>
    </div>
  );
};

export default ModuleLayout;
