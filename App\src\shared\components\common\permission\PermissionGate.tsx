import React, { ReactNode } from "react";
import { useMsal } from "@azure/msal-react";
import { PERMISSIONS } from "../../../../config/permission";

type Props = {
  children?: ReactNode;
  scopes?: string[];
  condition?: () => boolean;
  renderError?: () => ReactNode;
  restrictedProps?: (Partial<any> & React.Attributes) | undefined;
};

const hasPermission = (permissions: string[], scopes: string[]) => {
  if (!scopes || scopes.length <= 0)
    return true;

  const scopesMap: any = {};
  scopes.forEach((scope) => {
    scopesMap[scope] = true;
  });

  return permissions.some((permission) => scopesMap[permission]);
};

const PermissionsGate: React.FC<Props> = ({
  children,
  scopes = [],
  condition = () => true,
  renderError = () => <></>,
  restrictedProps = null
}) => {
  const { accounts } = useMsal();
  const roles = accounts[0]?.['idTokenClaims']?.roles as string[];

  const permissions: string[] = [];
  roles.forEach((role) => {
    permissions.push(...PERMISSIONS[role.toLowerCase()]);
  });

  const permissionGranted = condition() && hasPermission(permissions, scopes);

  if (!permissionGranted && !restrictedProps) return renderError();

  // Make modifications to the child component's properties, like disabling rather than hiding it.
  // eg: errorProps={{ disabled: true }}
  if (!permissionGranted && restrictedProps)
    return React.cloneElement(children as React.ReactElement<any>, { ...restrictedProps });

  return <>{children}</>;
}

export default PermissionsGate;

