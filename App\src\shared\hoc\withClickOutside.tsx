import { useState, useRef, useEffect } from "react";

const withClickOutside = (WrappedComponent: any) => {
  const Component = () => {
    const [isOpen, setOpen] = useState(false);
    const node = useRef<HTMLDivElement>(null);

    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (node.current && !node.current.contains(event.target as Node)) {
          setOpen(false);
        }
      };

      document.addEventListener("mousedown", handleClickOutside);

      return () => {
        document.removeEventListener("mousedown", handleClickOutside);
      };
    }, []);

    return (
      <div ref={node}>
        <WrappedComponent isOpen={isOpen} setOpen={setOpen} />
      </div>
    );
  };

  return Component;
};

export default withClickOutside;
