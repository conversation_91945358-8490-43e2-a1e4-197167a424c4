import '@ant-design/v5-patch-for-react-19';
import { AuthProvider } from './providers/auth/AuthContext';
import { MenuProvider } from './providers/MenuProvider';
import ErrorBoundary from './shared/components/common/error-boundary/ErrorBoundary';
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import AppRouter from './AppRouter';

function App() {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <ToastContainer />
        <MenuProvider>
          <AppRouter />
        </MenuProvider>
      </AuthProvider>
    </ErrorBoundary>
  )
}

export default App;
