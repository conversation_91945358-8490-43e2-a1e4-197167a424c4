import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { FormOption, JForm, JFormSchema } from "../../../../shared/components/common/json-form";
import { permissionService, Permission } from "../../../../api/services/admin/permissionService";
import { message } from "antd";
import { Loader } from "../../../../shared/components/common";
import PermissionMatrix from "./PermissionMatrix";

const PermissionForm: React.FC<{ mode: "create" | "edit" }> = ({ mode }) => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [formData, setFormData] = useState<Permission | null>(null);
  const permissionId = Number(id);

  const schema: JFormSchema = {
    title: "Permission Management",
    formGroups: [
      {
        title: "",
        columns: 3,
        fields: [
          {
            name: "Id",
            type: "hidden",
            required: true
          },
          {
            name: "BusinessUnitId",
            type: "select",
            label: "Business Unit",
            required: true,
            options: async (): Promise<FormOption[]> => {
              const response = await permissionService.fetchAll();
              return response.BusinessUnitId.map(permissionService => ({
                label: permissionService.DisplayText,
                value: permissionService.Value
              }));
            }
          },
          {
            name: "ModuleId",
            type: "select",
            label: "Module",
            required: true,
            options: async (): Promise<FormOption[]> => {
              const response = await permissionService.fetchAll();
              return response.ModuleId.map(permissionService => ({
                label: permissionService.DisplayText,
                value: permissionService.Value
              }));
            },
          },
          {
            name: "RoleId",
            type: "select",
            label: "Role",
            required: true,
            options: async (): Promise<FormOption[]> => {
              const response = await permissionService.fetchAll();
              return response.RoleId.map(permissionService => ({
                label: permissionService.DisplayText,
                value: permissionService.Value
              }));
            },
          }
        ]
      }
    ]
  };

  useEffect(() => {
    if (mode === "edit" && permissionId) {
      const fetchPermission = async () => {
        try {
          const response = await permissionService.getById(permissionId);
          setFormData(response);
        } catch (error) {
          console.error("Error fetching permission data:", error);
        }
      };
      fetchPermission();
    }
  }, [mode, permissionId]);

  const handleSubmit = async (formValues: Record<string, any>) => {
    try {
      if (mode === "edit" && permissionId) {
        await permissionService.update(permissionId, formValues);
        message.success("Permission details have been updated successfully.");
      } else {
        await permissionService.create(formValues);
        message.success("The new permission has been created successfully.");
      }

      navigate("..");
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  };

  return (
    <>
    <div className="flex flex-col gap-2 bg-white p-[30px] rounded-[6px]">
      {mode === "edit" && !formData ? (
        <Loader />
      ) : (
        <JForm schema={schema} onSubmit={handleSubmit} data={formData || {}} />
      )}
    
    </div>
    <div className="mt-[15px]">
      {mode === "edit" && formData?.ModuleId && (
         <PermissionMatrix ModuleId={formData.ModuleId.toString()} />
            )}
    </div>
    </>

  );
};

export default PermissionForm;
