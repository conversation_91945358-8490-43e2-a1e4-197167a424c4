import React from "react";
import { Outlet } from "react-router-dom";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Footer, MainHeader, SidePanel } from "../../shared/components/render-elements";

const AdminLayout: React.FC = () => {
  return (
    <div className="min-h-screen flex">
      {/* Floating Side Panel */}
      <SidePanel />

      {/* Right Column: Header, Main Content, Footer */}
      <div className="flex-1 flex flex-col">
        <MainHeader />
        <main className="flex-1 p-4">
          {/* Display breadcrumbs below header */}
          <Breadcrumbs />
          <Outlet />
        </main>
        <MainFooter />
      </div>
    </div>
  );
};

export default AdminLayout;
