import React, { useState, useEffect, useRef } from "react";
import { FormField, FormOption } from "../types";
import styles from "./styles.module.css";

interface TreeSelectFieldProps {
  field: FormField;
  value: string | number;
  onChange: (name: string, value: string | number) => void;
}

const TreeSelectField: React.FC<TreeSelectFieldProps> = ({ field, value, onChange }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [expandedNodes, setExpandedNodes] = useState<Set<string | number>>(new Set());
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const toggleDropdown = () => setIsOpen((prev) => !prev);
  const toggleExpand = (nodeValue: string | number) =>
    setExpandedNodes((prev) => new Set(prev.has(nodeValue) ? [...prev].filter((v) => v !== nodeValue) : [...prev, nodeValue]));

  const handleSelect = (node: FormOption) => {
    onChange(field.name, node.value); // Select only the clicked item
    setIsOpen(false); // Close dropdown after selection
  };

  const renderTreeNodes = (nodes: FormOption[]) =>
    nodes.map((node) => (
      <div key={node.value} className={styles.treeNode}>
        <div className={styles.treeItem}>
          {node.children && (
            <span onClick={() => toggleExpand(node.value)} className={styles.toggleIcon}>
              {expandedNodes.has(node.value) ? (
                <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <polyline points="6 9 12 15 18 9" />
                </svg>
              ) : (
                <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <polyline points="9 6 15 12 9 18" />
                </svg>
              )}
            </span>
          )}
          <span className={styles.treeLabel} onClick={() => handleSelect(node)}>{node.label}</span>
        </div>
        {node.children && expandedNodes.has(node.value) && <div className={styles.treeChildren}>{renderTreeNodes(node.children)}</div>}
      </div>
    ));

  const flattenTree = (nodes: FormOption[]): FormOption[] => {
    return nodes.reduce<FormOption[]>((acc, node) => {
      acc.push(node);
      if (node.children) {
        acc.push(...flattenTree(node.children));
      }
      return acc;
    }, []);
  };

  const flatOptions = flattenTree(Array.isArray(field.options) ? field.options : []);
  const selectedLabel = flatOptions.find((node) => node.value === value)?.label || `Select ${field.label}`;

  return (
    <div className={styles.treeDropdown} ref={dropdownRef}>
      <div className={styles.treeDropdownSelected} tabIndex={0} onClick={toggleDropdown}>
        {selectedLabel}
        {/* Dropdown Icon */}
        <span className={styles.dropdownIcon}>
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#555" strokeWidth="2">
            <polyline points="6 9 12 15 18 9"></polyline>
          </svg>
        </span>
      </div>
      {isOpen && <div className={styles.treeDropdownMenu}>{renderTreeNodes(Array.isArray(field.options) ? field.options : [])}</div>}
    </div>
  );
};

export default TreeSelectField;
