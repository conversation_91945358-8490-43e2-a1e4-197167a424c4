import React, { useState } from 'react';

import BuilderHeader from './components/BuilderHeader';
import FormSections from './components/FormSections';
import FormElements from './components/FormElements';
import FormPreview from './components/FormPreview';
import { IFormElement, IFormSection, IModelForm, IModelDataElement } from "./types"

import { modelBuilderService } from '../../../../api/services/gms/modelBuilderService';

//import styles from "./styles.module.css";

interface FormBuilderProps {
  modelForm: IModelForm;
  setModelForm: (e: IModelForm) => void;
  formElements: IFormElement[];
  setFormElements: (e: IFormElement[]) => void;
  formSections: IFormSection[];
  setFormSections: (e: IFormSection[]) => void;
  dataElements: IModelDataElement[];
  onSave: () => void;
  onPublish: () => void;
}

const FormBuilder: React.FC<FormBuilderProps> = ({
  // modelForm,
  // setModelForm,
  formElements,
  setFormElements,
  formSections,
  setFormSections,
  dataElements,
  onSave,
  onPublish
}) => {

  const [language, setLanguage] = useState('en');
  const [selectedPath, setSelectedPath] = useState('');
  const [showPreview, setShowPreview] = useState(false);
  const [previewForm, setPreviewForm] = useState<Record<string, any>>({});

  return <div>

    {/* Header component */}
    <BuilderHeader
    title='ESMAP Application'
    language={language}
    onLanguageChange={setLanguage}
    />

    {/** form preview */}
    <FormPreview {...{
      schema: previewForm,
      showPreview,
      setShowPreview
    }} />

    <div className='w-full flex mt-4'>

      {/* navigation sections */}
      <div className='w-[25%] h-[69vh] overflow-y-auto bg-white mr-0 rounded-[6px]'>
        <FormSections
        language={language}
        sections={formSections}
        setSections={setFormSections}
        selectedPath={selectedPath}
        setSelectedPath={setSelectedPath} />
      </div>

      { /* form elements */}
      <div className='w-[75%]'>
        { selectedPath && (<FormElements
        dataElements={dataElements}
        elements={formElements}
        sections={formSections}
        setElements={setFormElements}
        elementPath={selectedPath}
        language={language}
        onSave={onSave}
        onPreview={async () => {
          var resp: Record<string, any> = await modelBuilderService.transformTeamplate({ elements: formElements, sections: formSections, title: {} });
          setPreviewForm(resp);
          setShowPreview(true)
        }}
        onPublish={onPublish}
        />) }
      </div>
    </div>
  </div>;
}

export default FormBuilder
