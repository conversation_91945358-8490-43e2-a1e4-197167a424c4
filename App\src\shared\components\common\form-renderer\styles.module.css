.topNavigationSection {
  background-color: #fff;
  border-radius: .5vw;
  height: 50px;
  position: sticky;
  top: 0;
  z-index: 5;
  margin-left: 0;
}

.topMenuTab {
    margin-left: 14px;
    margin-right: -9px;
    height: 34px;
    border: 1px solid #cccccc;
    background-color: #fff;
    color: #999999;
    border-radius: 6px;
    display: flex !important;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    font: 'Roboto Bold';
    font-size: 11px;
    cursor: pointer;
    text-transform: uppercase;
    margin-top: 7px;
    flex: 0 0 16% !important;
    padding: 3px 15px 5px 15px;
    line-height: 12px;
    max-width: 200px;
    width: 185px;
    text-align: center;
  }
  
  .topMenuTabActive {
    background-color: #0b3456;
    color: #FFF;
    font-size: 11px;
    cursor: default;
    text-align: center;
    line-height: 12px;
    display: flex !important;
    justify-content: center;
    align-items: center;
    position: relative;
  }

  .topMenuActiveTabArrow {
    background-image: url('/images/button/indicate-arrow_tab.png');
    background-repeat: no-repeat;
    display: block;
    position: absolute;
    width: 15px;
    height: 20px;
    top: 32px;
  }

  .leftNavigationContainer {
    padding: 30px 0 0;
    overflow: hidden;
    width: 100%
  }

  .subSectionItem {
    font-weight: bold;
    font-size: 14px;
    border-radius: revert;
    width: 260px;
    border-left: 3px solid rgb(255, 255, 255);
    color: rgb(128, 128, 128);
    position: relative;
    display: flex;
    align-items: center;
    padding-inline: 15px;
  }

  .subSectionContainer {
    overflow: unset !important;
    text-overflow: unset !important;
    padding: 8px 0;
    width: 100%;
    border-bottom: 1px solid #ebf0f2;
    cursor: pointer;
  }

  .subSectionIndex {
    background-color: #999;
    color: #fff;
    font-size: 11px;
    border-radius: 4px;
    margin-right: 15px;
    position: sticky;
    left: 0;
    right: 12px;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    padding: 5px 5px 5px 7px;
  }

  .selectedSubSectionItem {
    border-left: 3px solid rgb(11, 35, 63);
    color: rgb(0, 174, 239);
  }

.selectedSubSectionItem .subSectionIndex {
  background-color: #00aeef !important;
  cursor: default;
}

.slideExpand {
  background-image: url('/images/button/expand.png');
  width: 8px;
  height: 52px;
  display: flex;
  position: absolute;
  top: 200px;
  left: 6px;
}

.slideCollapse {
  background-image: url('/images/button/collapse.png');
  width: 8px;
  height: 52px;
  display: flex;
  position: absolute;
  top: 200px;
}

.navigationButton {
  display: flex;
  min-width: 100px;
  border: 1px solid #00aeef !important;
  color: #0b233f !important;
  height: 30px;
  font-size: 14px !important;
  font-weight: 500 !important;
  align-items: center;
  justify-content: center;
  text-transform: uppercase;
}


.navigationButton:hover {
  display: flex;
  min-width: 100px;
  border: 1px solid #0b233f !important;
  background-color: #0b233f !important;
  height: 30px;
  font-size: 14px;
  font-weight: 500;
  color: #fff !important;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  text-transform: uppercase;
}

.rightArrow {
  margin-left: 10px;
  align-items: center;
  justify-content: center;
  display: flex;
}

.leftArrow {
  margin-right: 10px;
  align-items: center;
  justify-content: center;
  display: flex;
}

.leftArrow:hover {
  color: #fff;
}
.navigationBtnText {
  font-size: 12px;
  font-weight: bold;
  line-height: 26px;
}