import React from "react";
import { Checkbox } from "antd";
import { CloseOutlined } from "@ant-design/icons";

type Option = {
    value: string;
    label: string;
};

interface MultiSelectDialogProps {
    isOpen: boolean;
    options: Option[];
    selectedMultiple: Option[];
    onClose: () => void;
    onCheck: (checked: boolean, option: Option) => void;
    onAdd: () => void;
}

const MultiSelectDialog: React.FC<MultiSelectDialogProps> = ({
    isOpen,
    options,
    selectedMultiple,
    onClose,
    onCheck,
    onAdd,
}) => {
    if (!isOpen) return null;

    const allSelected = options.length > 0 && selectedMultiple.length === options.length;

    const handleSelectAllChange = (checked: boolean) => {
        if (checked) {
            options.forEach(option => {
                if (!selectedMultiple.some(selected => selected.value === option.value)) {
                    onCheck(true, option);
                }
            });
        } else {
            selectedMultiple.forEach(option => {
                onCheck(false, option);
            });
        }
    };

    return (
        <div className="fixed inset-0 bg-opacity-50 flex items-center justify-center z-50">
            <div className="relative bg-white rounded-lg border shadow-lg w-4/5 max-w-[50%] max-h-[50%] p-6 m-5 flex flex-col">
                <button
                    onClick={onClose}
                    className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 text-xl"
                    aria-label="Close modal"
                    type="button"
                >
                    <CloseOutlined />
                </button>

                <div className="flex items-center mb-4">
                    <Checkbox
                        checked={allSelected}
                        onChange={(e) => handleSelectAllChange(e.target.checked)}
                    >
                        <span className="text-[20px] leading-[20px] text-[#0B3456] font-semibold">
                            Select DataElements
                        </span>
                    </Checkbox>
                </div>

                <div className="overflow-y-auto max-h-[40%] pr-[10px]">
                    <div className="space-y-3 pr-2">
                        {options.map((option) => {
                            const isChecked = selectedMultiple.some(
                                (s) => s.value === option.value
                            );
                            return (
                                <div key={option.value} className="flex items-center">
                                    <Checkbox
                                        checked={isChecked}
                                        onChange={(e) => onCheck(e.target.checked, option)}
                                    >
                                        {option.label}
                                    </Checkbox>
                                </div>
                            );
                        })}
                    </div>
                </div>

                <div className="mt-6 flex justify-end gap-3">
                    <button
                        className="px-4 h-[30px] bg-[#00AEEF] hover:bg-[#0B3456] text-white rounded"
                        onClick={onClose}
                    >
                        Cancel
                    </button>
                    <button
                        className="px-4 h-[30px] bg-[#00AEEF] hover:bg-[#0B3456] text-white rounded"
                        onClick={onAdd}
                    >
                        Add
                    </button>
                </div>
            </div>
        </div>
    );
};

export default MultiSelectDialog;
