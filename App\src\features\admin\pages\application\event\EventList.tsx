import React from "react";
import { MasterPage } from "../../../../../shared/components/render-elements";
import { ColumnSchema } from "../../../../../shared/components/common/data-table";

const eventSchema: ColumnSchema[] = [
  {
    key: "Id",
    title: "Id",
    hidden: true,
    editable: false,
    sorter: false
  },
  {
    key: "Code",
    title: "Code",
    editable: true,
    sorter: true,
    type: "text",
    filterable: true
  },
  {
    key: "Name",
    title: "Name",
    editable: true,
    sorter: true,
    type: "text",
    filterable: true
  },
  {
    key: "Description",
    title: "Description",
    editable: true,
    sorter: true,
    type: "text",
    filterable: true
  },
  {
    key: "EventTypeId",
    title: "Event Type",
    editable: true,
    sorter: true,
    type: "select",
    filterable: true
  }
];

const EventList: React.FC = () => {
  return <MasterPage
    entityName="Events"
    columnsSchema={eventSchema}
    mode="form"
    apiEndpoints={{
      create: `${import.meta.env.VITE_PGM_API_BASE_URL}/api/events`,
      edit: `${import.meta.env.VITE_PGM_API_BASE_URL}/api/events`,
      delete: `${import.meta.env.VITE_PGM_API_BASE_URL}/api/events`,
      list: `${import.meta.env.VITE_PGM_API_BASE_URL}/api/events`
    }}
    routeTemplates={{
      create: "/admin/application/events/create",
      edit: "/admin/application/events/:id/edit",
    }}
  />;
}

export default EventList;
