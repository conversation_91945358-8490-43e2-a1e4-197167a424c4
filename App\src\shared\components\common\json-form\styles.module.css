/* Form Container */
.jFormContainer {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-width: 100%;
  margin: 0;
  padding: 0;
}

.jFormContainer .formTitle {
  font-size: 16px;
  font-weight: 700;
  color: #0a233e;
  text-transform: capitalize;
}

/* Group Titles */
.jFormContainer fieldset {
  border: none;
  padding: 0;
  margin-bottom: 20px;
}

.jFormContainer legend {
  font-size: 1.5rem;
  font-weight: 500;
  margin-bottom: 10px;
  color: #333;
  text-transform: uppercase;
}

/* Labels */
.jFormContainer label {
  display: flex;
  justify-content: space-between;
  color: #555;
  margin-bottom: 8px;
}

.jFormContainer label .mandatory {
  color: red;
}

/* Info icon */
.tooltipWrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.tooltipWrapper .infoIcon {
  cursor: pointer;
  position: relative;
}

.tooltipContent {
  display: flex;
  position: absolute;
  z-index: 10;
  bottom: 120%;
  left: 50%;
  transform: translateX(-50%);
  background: #333;
  color: white;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  width: max-content;
  max-width: 250px;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s;
}

.tooltipContent::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-width: 6px;
  border-style: solid;
  border-color: #333 transparent transparent transparent;
}

.tooltipWrapper:hover .tooltipContent {
  visibility: visible;
  opacity: 1;
}

/* Input Fields */
.jFormContainer input[type="text"],
.jFormContainer input[type="email"],
.jFormContainer input[type="password"],
.jFormContainer input[type="number"],
.jFormContainer input[type="date"],
.jFormContainer select {
  width: 100%;
  padding: 5px;
  font-size: 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  outline: none;
  box-sizing: border-box;
  transition: border-color 0.3s ease;
}

.jFormContainer input[type="text"]:focus,
.jFormContainer input[type="email"]:focus,
.jFormContainer input[type="password"]:focus,
.jFormContainer input[type="number"]:focus,
.jFormContainer input[type="date"]:focus,
.jFormContainer select:focus {
  border-color: #0078d4;
  /* Microsoft blue */
}

/* Checkbox and Radio Fields */
.jFormContainer input[type="checkbox"],
.jFormContainer input[type="radio"] {
  margin-right: 8px;
}

.jFormContainer input[type="checkbox"]+label,
.jFormContainer input[type="radio"]+label {
  display: inline-block;
  font-weight: 400;
  color: #333;
}

/* Buttons */
.jFormContainer .formActions {
  display: flex;
  justify-content: center;
  gap: 2rem;
}

.jFormContainer .formActions button {
  height: 30px;
  padding: 3px 20px;
  color: #fff;
  background-color: #1e2939;
  /* Microsoft blue */
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  text-align: center;
}

.jFormContainer .formActions button:hover {
  background-color: #fff;
  color: #1e2939;
  outline: 1px solid #1e2939;
}

.jFormContainer .formActions button[disabled] {
  background-color: #ccc;
  cursor: not-allowed;
}

/* Grid Layout for Group Columns */
.jFormContainer fieldset>div {
  display: grid;
  gap: 20px;
}

.jFormContainer fieldset.columns-1>div {
  grid-template-columns: 1fr;
}

.jFormContainer fieldset.columns-2>div {
  grid-template-columns: 1fr 1fr;
}

/* Conditional Rendering */
.jFormContainer .hidden {
  display: none;
}

/* Form Spacing */
.jFormContainer form>*:not(:last-child) {
  margin-bottom: 15px;
}

.jFormContainer .errorMessage {
  color: red;
  font-size: 0.9rem;
  margin-top: 0.2rem;
  display: block;
}
