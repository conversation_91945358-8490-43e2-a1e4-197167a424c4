# API Configuration
VITE_API_BASE_URL="http://localhost:5001"

# Authentication Providers
VITE_AUTH_PROVIDER="AzureAD"

# Azure AD Configurations
VITE_AZURE_AD_CLIENT_ID="cb6813cd-1aa7-4914-8ad5-8beff4285b3e"
VITE_AZURE_AD_AUTHORITY="https://login.microsoftonline.com/93708259-3510-4cf8-bfab-b0802727f5e4"
VITE_AZURE_AD_KNOWN_AUTHORITIES="infraleadspanapps.onmicrosoft.com"
VITE_AZURE_AD_LOGIN_SCOPES="openid,profile,offline_access,api://c9adab45-6d38-4c45-b226-e9ad2dbf8608/AGRA.Read"
VITE_AZURE_AD_REDIRECT_URI="/"
VITE_AZURE_AD_POST_LOGOUT_REDIRECT_URI="/"
