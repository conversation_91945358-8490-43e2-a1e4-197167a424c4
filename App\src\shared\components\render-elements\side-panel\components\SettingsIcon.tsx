import React from 'react';

interface SettingsIconProps extends React.SVGProps<SVGSVGElement> {
    className?: string;
    isActive?: boolean;
}

const SettingsIcon: React.FC<SettingsIconProps> = ({ className, isActive, ...rest }) => {
    return isActive ? (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            version="1.1"
            x="0px"
            y="0px"
            width="25.5px"
            height="25.5px"
            viewBox="0 0 25.5 25.5"
            xmlSpace="preserve"
            className={className}
            {...rest}
        >
            <g>
                <path
                    fill="#0B233F"
                    d="M21.2,25.2h-17c-2.2,0-4-1.8-4-4v-17c0-2.2,1.8-4,4-4h17c2.2,0,4,1.8,4,4v17C25.2,23.5,23.5,25.2,21.2,25.2z"
                />
                <g>
                    <g>
                        <g>
                            <g>
                                <path
                                    fill="#00AEEF"
                                    d="M20.2,13.7c0,0.1-0.1,0.2-0.2,0.2h-1.5c-0.5,0-0.9,0.3-1.1,0.7c-0.2,0.5-0.1,0.9,0.3,1.3l1.1,1.1
                    c0.1,0.1,0.1,0.2,0,0.3l-1.4,1.4c-0.1,0.1-0.2,0.1-0.3,0L16,17.7c-0.3-0.3-0.8-0.4-1.3-0.3c-0.4,0.2-0.7,0.6-0.7,1.1V20
                    c0,0.1-0.1,0.2-0.2,0.2h-1.9c-0.1,0-0.2-0.1-0.2-0.2v-1.5c0-0.5-0.3-0.9-0.7-1.1c-0.2-0.1-0.3-0.1-0.5-0.1
                    c-0.3,0-0.6,0.1-0.8,0.3l-1.1,1.1c-0.1,0.1-0.2,0.1-0.3,0l-1.4-1.4c-0.1-0.1-0.1-0.2,0-0.3L7.9,16c0.3-0.3,0.4-0.8,0.3-1.3
                    c-0.2-0.4-0.6-0.7-1.1-0.7H5.5c-0.1,0-0.2-0.1-0.2-0.2v-1.9c0-0.1,0.1-0.2,0.2-0.2H7c0.5,0,0.9-0.3,1.1-0.7
                    c0.2-0.5,0.1-0.9-0.3-1.3L6.8,8.5c-0.1-0.1-0.1-0.2,0-0.3l1.4-1.4c0.1-0.1,0.2-0.1,0.3,0l1.1,1.1c0.3,0.3,0.8,0.4,1.3,0.3
                    c0.4-0.2,0.7-0.6,0.7-1.1V5.5c0-0.1,0.1-0.2,0.2-0.2h1.9c0.1,0,0.2,0.1,0.2,0.2V7c0,0.5,0.3,0.9,0.7,1.1
                    c0.4,0.2,0.9,0.1,1.3-0.3L17,6.8c0.1-0.1,0.2-0.1,0.3,0l1.4,1.4c0.1,0.1,0.1,0.2,0,0.3l-1.1,1.1c-0.3,0.3-0.4,0.8-0.3,1.3
                    c0.2,0.4,0.6,0.7,1.1,0.7H20c0.1,0,0.2,0.1,0.2,0.2V13.7z"
                                />
                            </g>
                            <path
                                fill="#0B233F"
                                d="M12.8,14.3c-0.9,0-1.6-0.7-1.6-1.6s0.7-1.6,1.6-1.6c0.9,0,1.6,0.7,1.6,1.6S13.6,14.3,12.8,14.3z"
                            />
                        </g>
                    </g>
                </g>
            </g>
        </svg>
    ) : (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            x="0px"
            y="0px"
            width="25.5px"
            height="25.5px"
            viewBox="0 0 25.5 25.5"
            className={className}
            {...rest}
        >
            <g>
                <path
                    fill="#00AEEF"
                    d="M21.2,25.2h-17c-2.2,0-4-1.8-4-4v-17c0-2.2,1.8-4,4-4h17c2.2,0,4,1.8,4,4v17C25.2,23.5,23.5,25.2,21.2,25.2z"
                />
                <g>
                    <g>
                        <g>
                            <g>
                                <path
                                    fill="#0B233F"
                                    d="M20.2,13.7c0,0.1-0.1,0.2-0.2,0.2h-1.5c-0.5,0-0.9,0.3-1.1,0.7c-0.2,0.5-0.1,0.9,0.3,1.3l1.1,1.1
                    c0.1,0.1,0.1,0.2,0,0.3l-1.4,1.4c-0.1,0.1-0.2,0.1-0.3,0L16,17.7c-0.3-0.3-0.8-0.4-1.3-0.3c-0.4,0.2-0.7,0.6-0.7,1.1V20
                    c0,0.1-0.1,0.2-0.2,0.2h-1.9c-0.1,0-0.2-0.1-0.2-0.2v-1.5c0-0.5-0.3-0.9-0.7-1.1c-0.2-0.1-0.3-0.1-0.5-0.1
                    c-0.3,0-0.6,0.1-0.8,0.3l-1.1,1.1c-0.1,0.1-0.2,0.1-0.3,0l-1.4-1.4c-0.1-0.1-0.1-0.2,0-0.3L7.9,16c0.3-0.3,0.4-0.8,0.3-1.3
                    c-0.2-0.4-0.6-0.7-1.1-0.7H5.5c-0.1,0-0.2-0.1-0.2-0.2v-1.9c0-0.1,0.1-0.2,0.2-0.2H7c0.5,0,0.9-0.3,1.1-0.7
                    c0.2-0.5,0.1-0.9-0.3-1.3L6.8,8.5c-0.1-0.1-0.1-0.2,0-0.3l1.4-1.4c0.1-0.1,0.2-0.1,0.3,0l1.1,1.1c0.3,0.3,0.8,0.4,1.3,0.3
                    c0.4-0.2,0.7-0.6,0.7-1.1V5.5c0-0.1,0.1-0.2,0.2-0.2h1.9c0.1,0,0.2,0.1,0.2,0.2V7c0,0.5,0.3,0.9,0.7,1.1
                    c0.4,0.2,0.9,0.1,1.3-0.3L17,6.8c0.1-0.1,0.2-0.1,0.3,0l1.4,1.4c0.1,0.1,0.1,0.2,0,0.3l-1.1,1.1c-0.3,0.3-0.4,0.8-0.3,1.3
                    c0.2,0.4,0.6,0.7,1.1,0.7H20c0.1,0,0.2,0.1,0.2,0.2V13.7z"
                                />
                            </g>
                            <path
                                fill="#00AEEF"
                                d="M12.8,14.3c-0.9,0-1.6-0.7-1.6-1.6s0.7-1.6,1.6-1.6c0.9,0,1.6,0.7,1.6,1.6S13.6,14.3,12.8,14.3z"
                            />
                        </g>
                    </g>
                </g>
            </g>
        </svg>
    );
};

export default SettingsIcon;
