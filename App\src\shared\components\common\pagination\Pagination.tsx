import React from 'react';

type Props = {
  totalRows: number;
  pageIndex: number;
  pageSize: number;
  showPageNumber?: boolean;
  showFirstLast?: boolean;
  showPageInfo?: boolean;
  onPageChange: (page: number) => void;
}

const Pagination: React.FC<Props> = ({
  totalRows,
  pageIndex,
  pageSize,
  showPageNumber = true,
  showFirstLast = true,
  showPageInfo = true,
  onPageChange
}) => {
  const totalPages = Math.ceil(totalRows / pageSize);

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      onPageChange(page);
    }
  };

  const renderPageNumbers = () => {
    const pages = [];
    const maxPagesToShow = 5;
    const startPage = Math.max(1, pageIndex - Math.floor(maxPagesToShow / 2));
    const endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);

    if (startPage > 1) {
      pages.push(
        <button key="1" className="pl-[10px]" onClick={() => handlePageChange(1)}>
          1
        </button>
      );
      if (startPage > 2) {
        pages.push(<span key="start-ellipsis" className="pl-[10px]">...</span>);
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <button
          key={i}
          className={`pl-[10px] disabled:text-[#808080] ${pageIndex === i ? 'text-[#808080]' : ''}`}
          disabled={pageIndex === i}
          onClick={() => handlePageChange(i)}
        >
          {i}
        </button>
      );
    }

    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        pages.push(<span key="end-ellipsis" className="pl-[10px]">...</span>);
      }
      pages.push(
        <button key={totalPages} className="pl-[10px]" onClick={() => handlePageChange(totalPages)}>
          {totalPages}
        </button>
      );
    }

    return pages;
  };

  return (
    <div className="flex items-center justify-between space-x-[10px] divide-x divide-ccc pt-[20px] text-primary text-13 font-normal">
      {showFirstLast &&
        <button
          className="flex gap-[10px] pl-[10px] disabled:text-[#808080]"
          onClick={() => handlePageChange(1)}
          disabled={pageIndex === 1}
        >
          <span>&laquo;</span> First
        </button>
      }
      <button
        className="flex gap-[10px] pl-[10px] disabled:text-[#808080]"
        onClick={() => handlePageChange(pageIndex - 1)}
        disabled={pageIndex === 1}
      >
        <span>&lsaquo;</span> Previous
      </button>
      {showPageNumber && renderPageNumbers()}
      {showPageInfo && <span className="pl-[10px] text-333">Displaying {((pageIndex - 1) * pageSize) + 1} - {Math.min(pageIndex * pageSize, totalRows)} of {totalRows} results</span>}
      <button
        className="flex gap-[10px] pl-[10px] disabled:text-[#808080]"
        onClick={() => handlePageChange(pageIndex + 1)}
        disabled={pageIndex === totalPages}
      >
        Next <span>&rsaquo;</span>
      </button>
      {showFirstLast &&
        <button
          className="flex gap-[10px] pl-[10px] disabled:text-[#808080]"
          onClick={() => handlePageChange(totalPages)}
          disabled={pageIndex === totalPages}
        >
          Last <span>&raquo;</span>
        </button>
      }
    </div>
  );
};

export default Pagination;
