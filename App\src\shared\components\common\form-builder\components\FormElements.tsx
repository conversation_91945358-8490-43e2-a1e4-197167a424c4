import React, { useState } from 'react';
import { Button } from 'antd';
import { IFormElement, IFormSection, IModelDataElement } from '../types';
import { PlusOutlined, DeleteOutlined, UpCircleOutlined, DownCircleOutlined } from '@ant-design/icons';
import ElementPicker from './ElementPicker';
import ElementBuilder from './ElementBuilder';
import { ContextMenu } from '../../context-menu';
import styles from "../styles.module.css";
import { handleElementDelete } from '../utils/deletionHandlers';
import { moveElementUp, moveElementDown } from '../utils/elementSort';
import DragDrop from '../../drag-drop/DragDrop';
import { toast } from "react-toastify";

interface FormElemementProps {
    sections: IFormSection[];
    elements: IFormElement[];
    setElements: (e: IFormElement[]) => void;
    dataElements: IModelDataElement[];
    elementPath: string;
    language: string;
    onSave: () => void;
    onPreview: () => void;
    onPublish: () => void;
}

const FormElements: React.FC<FormElemementProps> = ({
    sections,
    elements,
    setElements,
    dataElements,
    elementPath,
    language,
    onSave,
    onPreview,
    onPublish
}) => {

    const [showPicker, setShowPicker] = useState(Boolean);
    const [isSaveDisabled, setIsSaveDisabled] = useState(false);
    const [isPublishDisabled, setIsPublishDisabled] = useState(false);

    // get the nested sections by path
    const getSection = (level: number) => {
        const keys = elementPath.split('.').slice(0, level + 1); // Split the qualified path into keys
        return keys.reduce((acc: any, key: string) => {
            if (acc.children && acc.children.findIndex((x: any) => x.key == key) >= 0) {
                return acc.children.find((x: any) => x.key == key);
            }
        }, { children: sections });
    }

    const handleSave = async () => {
        setIsSaveDisabled(true);
        try {
            await onSave();
            toast.success("Form saved successfully!");
        } catch (err) {
            toast.error("Failed to save the form.");
        }
        setTimeout(() => setIsSaveDisabled(false), 3000);
    };

    const handlePublish = async () => {
        setIsPublishDisabled(true);
        try {
            await onPublish();
            toast.success("Form published successfully!");
        } catch (err) {
            toast.error("Failed to publish the form.");
        }
        setTimeout(() => setIsPublishDisabled(false), 3000);
    };

    return (
        <>
            <div className='w-[calc(100%-20px)] h-[calc(100vh-294px)] ml-5 overflow-auto'>
                <div className='h-[62px] flex'>
                    {
                        elementPath.split('.').map((key, level) => {
                            if (level > 0) {
                                return (
                                    <React.Fragment key={`section_${level}_${key}`}>
                                        <span className={styles.elementSectionGap} />
                                        <span className={styles[`elementSection${level + 1}`]}>
                                            {getSection(level)?.title?.[language]}
                                        </span>
                                    </React.Fragment>
                                );
                            } else {
                                return (
                                    <span key={`section_${level}_${key}`} className={styles[`elementSection${level + 1}`]}>
                                        {getSection(level)?.title?.[language]}
                                    </span>
                                );
                            }
                        })
                    }
                </div>
                <div className='w-[100%] border-t border-solid border-[#0092cd] mb-[5px] mt-[2px]'></div>

                {/* elements */}
                <DragDrop
                    items={elements?.filter(x => x.elementPath === elementPath) || []}
                    onDrop={(newItems) => {
                        const unchanged = elements.filter(x => x.elementPath !== elementPath);
                        setElements([...unchanged, ...newItems]);
                    }}
                    getKey={(item) => `element_${item.elementId}`}
                    contextId={elementPath}
                    renderItem={(e, i) => {
                        return (
                            <div key={`element_${e.elementId}`} className='w-[100%] flex'>
                                <ElementBuilder
                                    element={e}
                                    elements={elements}
                                    i={i}
                                    language={language}
                                    setElements={setElements}
                                />
                                <div className='p-2.5'>
                                    <ContextMenu
                                        items={[
                                            {
                                                key: 'delete',
                                                dispayText: 'Delete',
                                                icon: DeleteOutlined,
                                                onClick: () => handleElementDelete(e, elements, setElements)
                                            },
                                            {
                                                key: 'moveUp',
                                                dispayText: 'Move Up',
                                                icon: UpCircleOutlined,
                                                onClick: () => moveElementUp(e, elements, setElements)
                                            },
                                            {
                                                key: 'moveDown',
                                                dispayText: 'Move Down',
                                                icon: DownCircleOutlined,
                                                onClick: () => moveElementDown(e, elements, setElements)
                                            }
                                        ]}
                                    />
                                </div>
                            </div>
                        );
                    }}
                />
                {/* button panel */}
                <div className='h-[62px] flex justify-end text-[14px] font-normal mr-[50px] items-center'>
                    <span>Add Element</span>
                    <span className={styles.addSectionBtn}>
                        <PlusOutlined style={{ fontSize: "12px" }} onClick={() => setShowPicker(true)} />
                    </span>
                </div>
                {/* element picker */}
                <ElementPicker
                    showPicker={showPicker}
                    setShowPicker={setShowPicker}
                    dataElements={dataElements}
                    onAdd={(rows) => {
                        let arrElements = [...(elements || [])];
                        arrElements = [...arrElements, ...rows.map((x: any) => ({
                            slug: x.Slug,
                            title: { 'en': x.Name },
                            dataType: x.DataType,
                            description: {},     
                            optionsFromURL: (x.OptionsFromURL)? `${import.meta.env.VITE_PGM_API_BASE_URL}/api/${x.OptionsFromURL}`: '',                       
                            elementId: Date.now() + Math.floor(Math.random() * 1000),
                            elementPath
                        } as IFormElement))];
                        setElements(arrElements);
                        setShowPicker(false);
                    }}
                />
            </div>

            {/* button panel */}
            <div className='w-[calc(100%-20px)] h-[60px] bg-white ml-5 flex'>
                <div className='w-[60%]'></div>
                <div className='w-[40%] flex  items-center justify-center'>
                    <Button
                        className={styles.btnDefault}
                        onClick={onPreview}
                    >
                        Preview
                    </Button>
                    <Button
                        className={styles.btnDefault}
                        onClick={handleSave}
                        disabled={isSaveDisabled}
                    >
                        Save
                    </Button>
                    <Button
                        className={styles.btnDefault}
                        onClick={handlePublish}
                        disabled={isPublishDisabled}
                    >
                        Publish
                    </Button>
                </div>
            </div>
        </>
    );
}

export default FormElements;
