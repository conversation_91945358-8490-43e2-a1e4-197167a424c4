export type AuthProviderType = 'CustomJWT' | 'AzureAD';

export interface AuthContextType {
  authType: AuthProviderType | null;
  authInProgress: boolean;
  user: AuthUser | null;
  login: (credentials?: AuthCredential) => Promise<void>;
  logout: () => void;
}

export interface AuthUser {
  name?: string;
  email?: string;
  access_token: string;
  id_token: string;
}

export interface AuthCredential {
  username: string;
  password: string;
}

export enum AuthEventType {
  LOGIN_SUCCESS = "LOGIN_SUCCESS",
  LOGIN_FAILURE = "LOGIN_FAILURE",
  LOGOUT_SUCCESS = "LOGOUT_SUCCESS",
  LOGOUT_FAILURE = "LOGOUT_FAILURE",
}
