import React from "react";
import { MasterPage } from "../../../../shared/components/render-elements";
import { ColumnSchema } from "../../../../shared/components/common/data-table/types";

interface LookUpListProps {
    LookUpTypeId: number;
}

const lookUpSchema: ColumnSchema[] = [
  {
    key: "Id",
    title: "Id",
    hidden: true,
    editable: false,
    sorter: false
  },
  {
    key: "Code",
    title: "Code",
    editable: true,
    sorter: true,
    filterable: true,
    type: "text"
  },
  
  {
    key: "SortOrder",
    title: "Sort Order",
    editable: true,
    sorter: true,
    filterable: true,
    type: "number"
  },
  {
    key: "Name",
    type: "text",
    title: "Description",
   editable: false,
    
  },
  {
    key: "IsSystemDefined",
    type: "text",
    title: "IsSystemDefined",
   
  },
  

];

const LookUpList: React.FC<LookUpListProps> = ({ LookUpTypeId }) => {
  return (
    <MasterPage
      entityName="Lookup"
      columnsSchema={lookUpSchema}
      mode="modal"
      modal={{
        formSchema: {
          title: "Lookup Management",
          formGroups: [
            {
              title: "",
              columns: 2,
              fields: [
                {
                  name: "Id",
                  type: "hidden",
                  required: true,
                  defaultValue: 0
                },
                {
                  name: "LookUpTypeId",
                  type: "hidden",
                  required: true,
                  defaultValue: LookUpTypeId
                },
                {
                  name: "Code",
                  type: "text",
                  label: "Code",
                  required: true
                },
                {
                    name: "Name",
                    type: "text",
                    label: "Description",
                    required: true
                  },
            
                {
                  name: "SortOrder",
                  type: "number",
                  label: "Sort Order",
                  required: false
                },
                {
                    name: "IsSystemDefined",
                    type: "toggle",
                    label: "IsSystemDefined",
                    required: true,
                    transform: (value) => (value ? 1 : 0),
                    defaultValue: 1,
                    validation: {
                      required: true,
                      custom: (value) => {
                        const num = Number(value)
                        return num === 0 || num === 1 ? undefined : "Value must be 0 or 1";
                      },
                    }
                  },
              ]
            }
          ]
        },
        width: 50
      }}
      apiEndpoints={{
        create: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/lookups`,
        edit: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/lookups`,
        delete: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/lookups`,
        list: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/lookups?id=${LookUpTypeId}`
      }}
    />
  );
};

export default LookUpList;
