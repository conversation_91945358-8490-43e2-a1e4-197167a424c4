import React from "react";
import Render<PERSON>ield from "./RenderField";
import { FormAction, JFormSchema } from "./types";
import { useFormState } from "./hooks";
import { validateForm } from "./utils/validation";
import styles from "./styles.module.css";

interface JFormProps {
  schema: JFormSchema; // Full schema for the form
  data?: Record<string, any>; // Prefilled data for editing the form
  onSubmit: (formData: Record<string, any>) => void; // Submission handler
  onFormChange?: (formData: Record<string, any>) => void; // Live form data callback
  transformError?: (errorMessage: string, fieldName: string) => string; // Custom error transformation
}

const JForm: React.FC<JFormProps> = ({ schema, data, onSubmit, onFormChange, transformError }) => {
  const { formData, errors, updateField, resetForm, setFieldErrors } = useFormState(
    data,
    [...(schema.formGroups?.flatMap((group) => group.fields) || []), ...(schema.fields || [])], // Pass all fields
    onFormChange,
    transformError
  );

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    const allFields = [
      ...(schema.formGroups?.flatMap((group) => group.fields) || []),
      ...(schema.fields || []),
    ];

    const validationErrors = validateForm(allFields, formData);

    if (Object.keys(validationErrors).length > 0) {
      setFieldErrors(validationErrors);
      return;
    }

    onSubmit(formData);
  };

  const handleReset = () => {
    resetForm(); // Reset form data
    setFieldErrors({}); // Clear errors
  };

  // Populate formData with data on mount or schema change
  // useEffect(() => {
  //   if (data) {
  //     setFormData(data);
  //   }
  // }, [data, setFormData]);

  return (
    <div className={styles.jFormContainer}>
      {typeof schema.title === "string" ? <h2 className={styles.formTitle}>{schema.title}</h2> : schema.title}
      <form autoFocus onSubmit={handleSubmit}>
        {/* Render grouped fields */}
        {schema?.formGroups?.map((group, index) => (
          <fieldset key={index} className={`fieldset columns-${group.columns}`}>
            <legend>{group.title}</legend>
            <div style={{ display: "grid", gridTemplateColumns: `repeat(${group.columns}, 1fr)`, gap: "1rem" }}>
              {group.fields.map((field) => (
                <RenderField
                  key={field.name}
                  field={field}
                  formData={formData}
                  error={errors[field.name]}
                  onChange={updateField}
                />
              ))}
            </div>
          </fieldset>
        ))}

        {/* Render ungrouped fields */}
        <div style={{ display: "grid", gridTemplateColumns: `repeat(4, 1fr)`, gap: "1rem" }}>
          {schema?.fields?.map((field) => (
            <RenderField
              key={field.name}
              field={field}
              formData={formData}
              error={errors[field.name]}
              onChange={updateField}
            />
          ))}
        </div>

        {/* Action Buttons */}
        {schema.actions?.showActions !== false && (
          <div className={styles.formActions}>
            {(schema.actions?.buttons?.length
              ? schema.actions.buttons
              : schema.actions?.defaultButtons !== false
                ? [{ type: "submit", label: "Submit", className: styles.submitButton }, { type: "reset", label: "Reset", className: styles.resetButton }] as FormAction[]
                : []
            ).map((action, index) => (
              <button
                key={index}
                type={action.type === "submit" ? "submit" : "button"}
                className={action.className}
                onClick={() => {
                  if (action.type === "reset") {
                    handleReset();
                  } else if (action.onClick) {
                    action.onClick(formData);
                  }
                }}
              >
                {action.label}
              </button>
            ))}
          </div>
        )}

      </form>
    </div>
  );
};

export default JForm;
