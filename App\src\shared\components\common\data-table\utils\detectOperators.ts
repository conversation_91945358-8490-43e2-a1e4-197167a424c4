export const OPERATOR_MAP: Record<string, string[]> = {
  text: ["contains", "equals", "starts with", "ends with"],
  textarea: ["contains", "equals", "starts with", "ends with"],
  number: ["equals", "not equals", ">", "<", "between"],
  date: ["equals", "not equals", "before", "after", "between"],
  select: ["equals", "not equals", "in"],
};

// Standard function to retrieve operators by type
export const getOperatorsByType = (filterType: string): string[] => OPERATOR_MAP[filterType] || [];
