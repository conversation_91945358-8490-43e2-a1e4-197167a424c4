import { lazy } from "react";
import { RouteObject } from "react-router-dom";
import SettingsPage from "./pages/SettingsPage";

const DashboardLayout = lazy(() => import("./DashboardLayout"));
const Dashboard = lazy(() => import("./pages/Dashboard"));

export const dashboardRoutes: RouteObject[] = [
  {
    path: "/",
    element: <DashboardLayout />,
    children: [
      { index: true, element: <Dashboard /> },
      { path: "settings", element: <SettingsPage /> }
    ]
  }
];
