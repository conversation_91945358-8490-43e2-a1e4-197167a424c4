import React from "react";
import { Select } from "antd";
import { DeleteOutlined } from "@ant-design/icons";
import MultiSelectDialog from "./MultiSelectDialog";

type Option = {
    value: string;
    label: string;
};

type DataElementRow = {
    selected: Option | null;
    displayName: string;
    description: string;
};

interface DataElementsProps {
    dataElementOptions: Option[];
    dataElementRows: DataElementRow[];
    setDataElementRows: React.Dispatch<React.SetStateAction<DataElementRow[]>>;
}

const DataElements: React.FC<DataElementsProps> = ({ dataElementOptions, dataElementRows, setDataElementRows }) => {
    const [isDialogOpen, setIsDialogOpen] = React.useState(false);
    const [selectedMultiple, setSelectedMultiple] = React.useState<Option[]>([]);

    const handleChange = (index: number, field: "selected" | "displayName" | "description", value: string) => {
        const updatedRows = [...dataElementRows];
        if (field === "selected") {
            const selected = dataElementOptions.find(opt => opt.value === value) || null;
            updatedRows[index].selected = selected;
            updatedRows[index].displayName = selected?.label || "";
        } else {
            updatedRows[index][field] = value;
        }
        setDataElementRows(updatedRows);
    };

    const handleAddRow = () => {
        const newRow: DataElementRow = {
            selected: null,
            displayName: "",
            description: "",
        };
        setDataElementRows([...dataElementRows, newRow]);
    };

    const handleDeleteRow = (index: number) => {
        const updatedRows = dataElementRows.filter((_, i) => i !== index);
        setDataElementRows(updatedRows);
    };

    const handleCheck = (checked: boolean, option: Option) => {
        setSelectedMultiple(prev =>
            checked
                ? [...prev, option]
                : prev.filter(item => item.value !== option.value)
        );
    };

    const handleAddMultiple = () => {
        const newRows: DataElementRow[] = selectedMultiple.map(option => ({
            selected: option,
            displayName: option.label,
            description: ""
        }));
        setDataElementRows([...dataElementRows, ...newRows]);
        setSelectedMultiple([]);
        setIsDialogOpen(false);
    };

    return (
        <div className="p-[20px] w-[1000px]">
            <h2 className="text-[20px] leading-[20px] text-[#0B3456] font-semibold mb-4">Program DataElements</h2>
            <div className="flex text-[#0B3456] font-medium mb-2">
                <div className="w-1/3 pr-2">DataElement</div>
                <div className="w-1/3 px-2">Display Name</div>
                <div className="w-1/3 px-2">Description</div>
            </div>
            <div className="space-y-4">
                {dataElementRows.map((row, index) => (
                    <div key={index} className="flex relative items-center">
                        <div className="w-1/3 pr-2">
                            <Select
                                showSearch
                                allowClear
                                value={row.selected?.value || undefined}
                                onChange={(value) => handleChange(index, "selected", value || "")}
                                options={dataElementOptions}
                                style={{ width: "100%", height: 40 }}
                                dropdownStyle={{ zIndex: 2000 }}
                                className="rounded border"
                                placeholder="Select DataElement"
                                filterOption={(input, option) =>
                                    (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
                                }
                            />
                        </div>
                        <div className="w-1/3 px-2">
                            <input
                                type="text"
                                value={row.displayName}
                                onChange={(e) => handleChange(index, "displayName", e.target.value)}
                                className="w-full h-[40px] rounded px-3 border"
                            />
                        </div>
                        <div className="w-1/3 px-2">
                            <input
                                type="text"
                                value={row.description}
                                onChange={(e) => handleChange(index, "description", e.target.value)}
                                className="w-full h-[40px] rounded px-3 border"
                            />
                        </div>
                        <div className="pl-1">
                            <DeleteOutlined
                                onClick={() => {
                                    if (dataElementRows.length > 1) {
                                        handleDeleteRow(index);
                                    }
                                }}
                                style={{
                                    color: dataElementRows.length <= 1 ? "#CCCCCC" : "#0B3456",
                                    cursor: dataElementRows.length <= 1 ? "not-allowed" : "pointer",
                                    fontSize: "18px",
                                }}
                            />
                        </div>
                    </div>
                ))}
            </div>
            <div className="mt-6 flex items-center">
                <button
                    className="px-4 h-[30px] bg-[#00AEEF] hover:bg-[#0B3456] text-white rounded flex items-center gap-x-1"
                    onClick={handleAddRow}
                >
                    Add
                </button>
                <button
                    className="ml-2 px-4 h-[30px] bg-[#00AEEF] hover:bg-[#0B3456] text-white rounded flex items-center gap-x-1"
                    onClick={() => setIsDialogOpen(true)}
                >
                    Add Multiple
                </button>
            </div>

            <MultiSelectDialog
                isOpen={isDialogOpen}
                options={dataElementOptions}
                selectedMultiple={selectedMultiple}
                onClose={() => setIsDialogOpen(false)}
                onCheck={handleCheck}
                onAdd={handleAddMultiple}
            />
        </div>
    );
};

export default DataElements;