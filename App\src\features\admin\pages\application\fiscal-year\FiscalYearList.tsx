import React from "react";
import { MasterPage } from "../../../../../shared/components/render-elements";
import { ColumnSchema } from "../../../../../shared/components/common/data-table";

const fiscalYearSchema: ColumnSchema[] = [
  {
    key: "Id",
    title: "Id",
    hidden: true,
    editable: false,
    sorter: false
  },
  {
    key: "FiscalYearCode",
    title: "Fiscal Year Code",
    editable: true,
    sorter: true,
    type: "text",
    filterable: true
  },
  {
    key: "StartDate",
    title: "Start Date",
    editable: true,
    sorter: true,
    type: "date",
    filterable: true
  },
  {
    key: "EndDate",
    title: "End Date",
    editable: true,
    sorter: true,
    type: "date",
    filterable: true
  },
  {
    key: "Active",
    title: "Active",
    editable: true,
    sorter: true,
    type: "select",
    filterable: true,
    options: [
      { label: "Yes", value: "1" },
      { label: "No", value: "0" }
    ]
  }
];

const FiscalYearList: React.FC = () => {
  return <MasterPage
    entityName="Fiscal Year"
    columnsSchema={fiscalYearSchema}
    mode="form"
    apiEndpoints={{
      create: `${import.meta.env.VITE_PGM_API_BASE_URL}/api/fiscal-year`,
      edit: `${import.meta.env.VITE_PGM_API_BASE_URL}/api/fiscal-year`,
      delete: `${import.meta.env.VITE_PGM_API_BASE_URL}/api/fiscal-year`,
      list: `${import.meta.env.VITE_PGM_API_BASE_URL}/api/fiscal-year`
    }}
    routeTemplates={{
      create: "/admin/application/fiscal-years/create",
      edit: "/admin/application/fiscal-years/:id/edit",
    }}
  />;
}

export default FiscalYearList;
