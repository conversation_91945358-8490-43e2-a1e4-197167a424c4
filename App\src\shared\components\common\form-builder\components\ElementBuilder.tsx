import React, { useState } from 'react';
import { Input, Select } from 'antd';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { IFormElement } from '../types';
import { getWidgetsByType } from '../utils/widgetSelector';
import TranslationIcon from './TranslationIcon';
import TranslationDialog from './TranslationDialog';
import styles from "../styles.module.css";
import ConditionalField from './ConditionalField';
import IncludeReport from './IncludeReport';
import SelectField from './SelectField';

interface ElementBuilderProps {
    language: string;
    element: IFormElement;
    elements: IFormElement[];
    setElements: (e: IFormElement[]) => void;
    i: number;
}

const ElementBuilder: React.FC<ElementBuilderProps> = ({
    language,
    element,
    elements,
    setElements,
    i
}) => {
    const [active, setActive] = useState(false);
    const [isDialogVisible, setDialogVisible] = useState(false);
    const [dialogText, setDialogText] = useState<string>('');
    const [dialogLanguageKey, setDialogLanguageKey] = useState<string>('');
    const [dialogFieldKey, setDialogFieldKey] = useState<'title' | 'description'>();
    const [existingDialogTranslations, setExistingDialogTranslations] = useState<{ [key: string]: string }>({});

    const translationIconClick = (languageKey: string, text: string, fieldKey: 'title' | 'description') => {
        setDialogLanguageKey(languageKey);
        setDialogText(text);
        setDialogFieldKey(fieldKey);

        const current = elements.find((el) => el.slug === element.slug);
        const allTranslations = current?.[fieldKey] || {};
        setExistingDialogTranslations(allTranslations);
        setDialogVisible(true);
    };

    const handleDialogClose = () => {
        setDialogVisible(false);
    };

    const handleDialogSubmit = (newTranslations: { [key: string]: string }) => {
        const arrElements = [...elements];
        const index = arrElements.findIndex((x) => x.slug === element.slug);

        if (index >= 0 && dialogFieldKey) {
            arrElements[index] = {
                ...arrElements[index],
                [dialogFieldKey]: {
                    ...(arrElements[index][dialogFieldKey] || {}),
                    ...newTranslations
                }
            };
            setElements(arrElements);
        }
    };

    const currentElement = elements.find((el) => el.slug === element.slug);

    // Conditional Field Data
    const subsectionElements = elements.filter(el => el.elementPath === element.elementPath);
    const conditionalDropdownOptions = subsectionElements.map(el => el.title?.[language] || el.slug);
    const operators = ["and", "or", "equals", "does not equal", "greater than", "less than"];
    const placeholders = ["Select condition", "Select operator", "Enter statement"];

    return (
        <div className={styles.elementContainer}>
            <div
                className={active ? styles.elementHeaderActiveRow : styles.elementHeaderRow}
                onClick={() => setActive((prev) => !prev)}
            >
                <span className={styles.elementIndex}><b>{i + 1}</b></span>
                <span className={styles.elementTitle}>{element.title?.[language]}</span>
                <span>{active ? <UpOutlined /> : <DownOutlined />}</span>
            </div>

            {active && (
                <div className={styles.elementContentRow}>
                    {/* Title */}
                    <div className="w-[100%] flex p-[20px]">
                        <div className="w-[calc(100%-60px)]">
                            <Input.TextArea
                                autoSize={{ minRows: 1, maxRows: 10 }}
                                placeholder="Title"
                                onChange={(e) => {
                                    const arrElements = [...elements];
                                    const index = arrElements.findIndex((x) => x.slug == element.slug);
                                    if (index >= 0) {
                                        arrElements[index] = {
                                            ...arrElements[index],
                                            title: { ...(arrElements[index].title || {}), [language]: e.target.value }
                                        };
                                        setElements(arrElements);
                                    }
                                }}
                                value={currentElement?.title?.[language] || ''}
                            />
                        </div>
                        <TranslationIcon onClick={() => translationIconClick(language, currentElement?.title?.[language] || '', 'title')} />
                    </div>

                    {/* Description */}
                    <div className="w-[100%] flex p-[20px]">
                        <div className="w-[calc(100%-60px)]">
                            <Input.TextArea
                                autoSize={{ minRows: 1, maxRows: 10 }}
                                placeholder="Description"
                                onChangeCapture={(e: any) => {
                                    const arrElements = [...elements];
                                    const index = arrElements.findIndex((x) => x.slug == element.slug);
                                    if (index >= 0) {
                                        arrElements[index] = {
                                            ...arrElements[index],
                                            description: { ...(arrElements[index].description || {}), [language]: e.target.value }
                                        };
                                        setElements(arrElements);
                                    }
                                }}
                                value={currentElement?.description?.[language] || ''}
                            />
                        </div>
                        <TranslationIcon onClick={() => translationIconClick(language, currentElement?.description?.[language] || '', 'description')} />
                    </div>

                    {/* Type */}
                    <div className="w-[100%] p-[20px]">
                        <div className="w-[100%] text-[16px] font-semibold mb-[20px]">Type</div>
                        <div className="w-[100%] flex">
                            <Select
                                className="w-[40%]"
                                value={currentElement?.widget}
                                options={getWidgetsByType(currentElement?.dataType || '')}
                                onChange={(e) => {
                                    const arrElements = [...elements];
                                    const index = arrElements.findIndex((x) => x.slug == element.slug);
                                    if (index >= 0) {
                                        arrElements[index] = {
                                            ...arrElements[index],
                                            widget: e
                                        };
                                        setElements(arrElements);
                                    }
                                }}
                            />
                        </div>
                        {(currentElement?.widget === 'multiselect' || currentElement?.widget === 'radio' || currentElement?.widget === 'select') && (
                            <div className="mt-4">
                                <SelectField
                                    element={element}
                                    elements={elements}
                                    setElements={setElements}
                                />
                            </div>
                        )}
                    </div>
                    <div>
                        <ConditionalField
                            dropdownOptions={[conditionalDropdownOptions, operators]}
                            placeholders={placeholders}
                        />
                    </div>
                    <div className='mt-[30px]'>
                        <IncludeReport />
                    </div>
                </div>
            )}
            {/* Translation Dialog */}
            <TranslationDialog
                visible={isDialogVisible}
                label={dialogText}
                languageKey={dialogLanguageKey}
                existingTranslations={existingDialogTranslations}
                onClose={handleDialogClose}
                onSubmit={handleDialogSubmit}
            />
        </div>
    );
};

export default ElementBuilder;
