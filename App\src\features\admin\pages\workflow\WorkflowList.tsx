import React from "react";
import { ColumnSchema } from "../../../../shared/components/common/data-table/types";
import { MasterPage } from "../../../../shared/components/render-elements";

const workflowSchema: ColumnSchema[] = [
  {
    key: "Id",
    title: "Id",
    hidden: true,
    sorter: false
  },
  {
    key: "Name",
    title: "Workflow Name",
    sorter: true,
    type: "text",
    filterable: true
  },
  {
    key: "Description",
    title: "Description",
    type: "text",
    filterable: true
  },
];

const WorkflowList: React.FC = () => {
  return <MasterPage
    entityName="Workflow"
    columnsSchema={workflowSchema}
    mode="form"
    apiEndpoints={{
      list: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/workflow-definition`
    }}
    routeTemplates={{
      create: "/admin/system/workflows/create",
      edit: "/admin/system/workflows/:id/edit",
    }}
  />;
};

export default WorkflowList;
