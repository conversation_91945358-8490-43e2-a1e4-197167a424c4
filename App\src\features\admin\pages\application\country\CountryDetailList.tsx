import React from "react";
import { MasterPage } from "../../../../../shared/components/render-elements";
import { ColumnSchema } from "../../../../../shared/components/common/data-table";

interface CountryDetailListProps {
  countryId: string;
}

const countryDetailSchema: ColumnSchema[] = [
  {
    key: "Id",
    title: "Id",
    hidden: true,
    editable: false,
    sorter: false
  },
  {
    key: "FiscalYearId",
    title: "FiscalYear",
    editable: true,
    sorter: true,
    type: "select",
    filterable: true
  },
  {
    key: "RegionId",
    title: "Region",
    editable: true,
    sorter: true,
    type: "select",
    filterable: true
  },
  {
    key: "IncomeGroupId",
    title: "IncomeGroup",
    editable: true,
    sorter: true,
    type: "select",
    filterable: true
  },
  {
    key: "FCSId",
    title: "FCS",
    editable: true,
    sorter: true,
    type: "select",
    filterable: true
  },
  {
    key: "SIDSId",
    title: "SIDS",
    editable: true,
    sorter: true,
    type: "select",
    filterable: true
  },
  {
    key: "LendingCategoryId",
    title: "LendingCategory",
    editable: true,
    sorter: true,
    type: "select",
    filterable: true
  }
];

const CountryDetailList: React.FC<CountryDetailListProps> = ({ countryId }) => {
  return <MasterPage
    entityName="Country Details"
    columnsSchema={countryDetailSchema}
    mode="modal"
    modal={{
      formSchema: {
        title: "Country Detail Management",
        formGroups: [
          {
            title: "",
            columns: 3,
            fields: [
              {
                name: "Id",
                type: "hidden",
                required: true,
                defaultValue: 0
              },
              {
                name: "CountryId",
                type: "hidden",
                required: true,
                defaultValue: countryId
              },
              {
                name: "FiscalYearId",
                type: "select",
                label: "Fiscal Year",
                required: true
              },
              {
                name: "RegionId",
                type: "select",
                label: "Region",
                required: true
              },
              {
                name: "IncomeGroupId",
                type: "select",
                label: "Income Group",
                required: true
              },
              {
                name: "FCSId",
                type: "select",
                label: "FCS",
                required: true
              },
              {
                name: "SIDSId",
                type: "select",
                label: "SIDS",
                required: true
              },
              {
                name: "LendingCategoryId",
                type: "select",
                label: "Lending Category",
                required: true
              }
            ]
          }
        ]
      },
      width: 70
    }}
    apiEndpoints={{
      create: `${import.meta.env.VITE_PGM_API_BASE_URL}/api/country-detail`,
      edit: `${import.meta.env.VITE_PGM_API_BASE_URL}/api/country-detail`,
      delete: `${import.meta.env.VITE_PGM_API_BASE_URL}/api/country-detail`,
      list: `${import.meta.env.VITE_PGM_API_BASE_URL}/api/country-detail?Id=${countryId}`
    }}
  />;
}

export default CountryDetailList;
