import { FormField, FormOption } from "../types";

/**
 * useFieldLogic - <PERSON>les conditional rendering and dependency resolution for form fields
 * @param formData - Current state of the form data
 * @returns Field logic handlers
 */
const useFieldLogic = (formData: Record<string, any>) => {
  /**
   * Determines if a field should be rendered based on `conditionalRender` logic
   * @param field - The form field definition
   * @returns true if the field should be rendered, false otherwise
   */
  const shouldRenderField = (field: FormField): boolean => {
    return field.conditionalRender ? field.conditionalRender(formData) : true; // Default to true
  };

  /**
   * Resolves field dependencies by filtering or modifying options based on other field values
   * @param field - The form field definition
   * @returns Resolved options for the field, or undefined if no options exist
   */
  const resolveFieldDependencies = (field: FormField): FormOption[] | undefined => {
    if (field.dependency && formData[field.dependency]) {
      const dependentValue = formData[field.dependency];

      // Ensure options exist and is an array before filtering
      if (Array.isArray(field.options)) {
        return field.options.filter((option) => option.value === dependentValue); // Filter options
      }
    }

    // Default to all options if dependency is null/undefined or dependency value is not found
    return Array.isArray(field.options) ? field.options : [];
  };

  return {
    shouldRenderField,
    resolveFieldDependencies,
  };
};

export default useFieldLogic;
