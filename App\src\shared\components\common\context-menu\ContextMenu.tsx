import React from 'react';
import { Dropdown } from 'antd';
import { ContextMenuItem } from './types';

import styles from "./styles.module.css";

interface ContextMenuProps {
  items: ContextMenuItem[]
}

interface DynamicIconProps {
  icon: React.ComponentType;
}

const DynamicIconComponent: React.FC<DynamicIconProps> = ({ icon: Icon }) => {
  return (<Icon />);
};

const ContextMenu: React.FC<ContextMenuProps> = ({ items }) => {
  return <>
    <Dropdown
      menu={{
        items: items.map((item) => {
          return {
            key: item.key,
            label: (<p
              style={{
                display: "flex",
                alignItems: "center",
              }}
              className={styles.contextMenuItem}
              onClick={() => { item.onClick ? item.onClick(item.key) : null }}
            >
              {item.icon ? <DynamicIconComponent icon={item.icon} /> : ''} &nbsp;
              <span className={styles.contextMenuItemText}>{item.dispayText}</span>
            </p>)
          }
        })
      }}
    >
      <span className={styles.contextMenuIcon} ></span>
    </Dropdown>
  </>;
}

export default ContextMenu;
