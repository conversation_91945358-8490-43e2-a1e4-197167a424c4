import React, { useState } from "react";

interface IncludeReportProps {
    isRequired?: boolean;
}

const IncludeReport: React.FC<IncludeReportProps> = ({ }) => {
    const [isChecked, setIsChecked] = useState(false);

    return (
        <div>
            <style>
                {`
                .custom-checkbox:checked::after {
                    content: '';
                    position: absolute;
                    top: 40%;
                    left: 50%;
                    margin-bottom: 3px;
                    width: 5px;
                    height: 10px;
                    border: solid white;
                    border-width: 0 2px 2px 0;
                    transform: translate(-50%, -50%) rotate(45deg);
                }

                .custom-checkbox {
                    position: relative;
                }
                `}
            </style>

            <div className="flex items-center gap-2 text-[14px] ml-[30px]">
                <input
                    type="checkbox"
                    checked={isChecked}
                    className="custom-checkbox w-[16px] h-[16px] appearance-none checked:bg-[#00AEEF] rounded-sm"
                    onChange={(e) => setIsChecked(e.target.checked)}
                />
                <span>Include in Report</span>
            </div>
        </div>
    );
};

export default IncludeReport;
