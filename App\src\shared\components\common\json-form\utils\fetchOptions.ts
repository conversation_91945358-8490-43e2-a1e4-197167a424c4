import apiClient from "../../../../../api/network/apiClient";
import { FormOption } from "../types";


export const fetchOptions = async (
  url: string,
  // getToken?: () => string | null,
  // customHeaders: Record<string, string> = {}
): Promise<FormOption[]> => {
  try {
    // const token = getToken?.();
    // const headers: Record<string, string> = {
    //   "Content-Type": "application/json",
    //   ...customHeaders
    // };

    // if (token) {
    //   headers["Authorization"] = `Bearer ${token}`;
    // }

    const response = await apiClient.get<FormOption[]>(url);

    return response.data;
  } catch (error) {
    console.error(`Error fetching options from ${url}:`, error);
    return [];
  }
};
