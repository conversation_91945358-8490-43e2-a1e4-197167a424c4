import apiClient from "../../network/apiClient";

// Define user structure
export interface Country {
  Id: number;
  Code: string;
  Description: string;
  Active: number;
  CreatedById?: number | null;
  CreatedAt?: string | null;
  UpdatedById?: number | null;
  UpdatedAt?: string | null;
  IsDeleted: boolean;
}

// Define fetch-all response structure
interface FetchResult {
  Record: Country[];
  UserRights: string;
}

const BASE_URL = `${import.meta.env.VITE_PGM_API_BASE_URL}/api/country`;

export const countryService = {
  // GET - Fetch all countries
  async fetchAll(): Promise<FetchResult | null> {
    try {
      const response = await apiClient.get<FetchResult>(BASE_URL);
      return response.data ?? null;
    } catch (error) {
      console.error("Error fetching countries:", error);
      return null;
    }
  },

  /**
 * GET - Fetch a single country by its ID.
 * @param countryId - The ID of the country to fetch.
 */
  async getById(countryId: number): Promise<Country> {
    try {
      const response = await apiClient.get<{ Record: Country }>(`${BASE_URL}/${countryId}`);
      return response.data.Record ?? null;
    } catch (error) {
      console.error(`Error fetching country with ID ${countryId}:`, error);
      throw error;
    }
  },

  // POST - Create a new country
  async create(country: Partial<Country>): Promise<Country | null> {
    try {
      const response = await apiClient.post<Country>(BASE_URL, country);
      return response.data ?? null;
    } catch (error) {
      console.error("Error creating country:", error);
      return null;
    }
  },

  // PUT - Update an existing country
  async update(countryId: number, updatedCountry: Partial<Country>): Promise<Country | null> {
    try {
      const response = await apiClient.put<Country>(`${BASE_URL}/${countryId}`, updatedCountry);
      return response.data ?? null;
    } catch (error) {
      console.error("Error updating country:", error);
      return null;
    }
  },

  // DELETE - Remove a country
  async delete(countryId: number): Promise<boolean> {
    try {
      await apiClient.delete(`${BASE_URL}/${countryId}`);
      return true;
    } catch (error) {
      console.error("Error deleting country:", error);
      return false;
    }
  }
};
