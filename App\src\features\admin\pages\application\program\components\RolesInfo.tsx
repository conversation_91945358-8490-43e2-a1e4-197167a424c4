import React from "react";
import { Select } from "antd";
import { DeleteOutlined } from "@ant-design/icons";

type Option = {
    value: string;
    label: string;
};

type RowData = {
    selectedRole: string;
    selectedModules: string[];
};

type RolesInfoProps = {
    rolesOptions: Option[];
    moduleOptions: Option[];
    rows: RowData[];
    setRows: React.Dispatch<React.SetStateAction<RowData[]>>;
};

const RolesInfo: React.FC<RolesInfoProps> = ({ rolesOptions, moduleOptions, rows, setRows }) => {
    const handleRoleChange = (index: number, value: string) => {
        const updatedRows = [...rows];
        updatedRows[index].selectedRole = value;
        setRows(updatedRows);
    };

    const handleModulesChange = (index: number, value: string[]) => {
        const updatedRows = [...rows];
        updatedRows[index].selectedModules = value;
        setRows(updatedRows);
    };

    const handleAddRow = () => {
        setRows([...rows, { selectedRole: "", selectedModules: [] }]);
    };

    const handleDeleteRow = (index: number) => {
        const updatedRows = rows.filter((_, i) => i !== index);
        setRows(updatedRows);
    };

    return (
        <div className="p-[20px] w-[800px]">
            <h2 className="text-[20px] leading-[20px] text-[#0B3456] font-semibold mb-4">Program Roles</h2>
            <div className="flex text-[#0B3456] font-medium mb-2">
                <div className="w-1/2 pr-2">Role</div>
                <div className="w-1/2 px-2">Modules</div>
            </div>
            <div className="space-y-4">
                {rows.map((row, index) => (
                    <div key={index} className="flex items-center relative">
                        <div className="w-1/2 pr-2">
                            <Select
                                showSearch
                                allowClear
                                value={row.selectedRole || undefined}
                                onChange={(value) => handleRoleChange(index, value || "")}
                                options={rolesOptions}
                                style={{ width: "100%", height: 40 }}
                                dropdownStyle={{ zIndex: 2000 }}
                                className="rounded border"
                                placeholder="Select Role"
                                filterOption={(input, option) =>
                                    (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
                                }
                            />
                        </div>
                        <div className="w-1/2 px-2">
                            <Select
                                mode="multiple"
                                allowClear
                                showSearch
                                value={row.selectedModules}
                                onChange={(value) => handleModulesChange(index, value)}
                                options={moduleOptions}
                                style={{ width: "100%", height: 40 }}
                                dropdownStyle={{ zIndex: 2000 }}
                                className="rounded border"
                                popupMatchSelectWidth
                                maxTagCount="responsive"
                                filterOption={(input, option) =>
                                    (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
                                }
                            />
                        </div>
                        <div className="pl-1">
                            <DeleteOutlined
                                onClick={() => {
                                    if (rows.length > 1) {
                                        handleDeleteRow(index);
                                    }
                                }}
                                style={{
                                    color: rows.length <= 1 ? "#CCCCCC" : "#0B3456",
                                    cursor: rows.length <= 1 ? "not-allowed" : "pointer",
                                    fontSize: "18px",
                                }}
                            />
                        </div>
                    </div>
                ))}
                <div className="mt-6">
                    <button
                        className="px-4 h-[30px] bg-[#00AEEF] hover:bg-[#0B3456] text-white rounded flex items-center gap-x-1"
                        onClick={handleAddRow}
                    >
                        Add
                    </button>
                </div>
            </div>
        </div>
    );
};

export default RolesInfo;