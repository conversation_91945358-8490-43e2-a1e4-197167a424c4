import React, { useEffect, useState } from "react";
import WorkflowDefinition from "../components/Definition";
import WorkflowStates from "../components/States";
import WorkflowTransitionLayout from "./WorkflowTransitionLayout";
import Sidebar from "../components/Sidebar";
import Wizard from "../../../../../shared/components/render-elements/wizard/Wizard";
import { Workflow } from "../types/types";

const LayoutComponent: React.FC = () => {
  const [definition, setDefinition] = useState<any>(null);
  const [states, setStates] = useState<any[]>([]);
  const [transition, setTransition] = useState<any>(null);

  const handleSaveAll = async () => {
    (window as any).saveWorkflowDefinition?.();
    (window as any).saveWorkflowStates?.();
    (window as any).saveWorkflowTransition?.();

    setTimeout(() => {
      const fullWorkflow: Workflow = {
        name: definition?.Name,
        description: definition?.Description,
        action: definition?.Action,
        table: definition?.Table,
        conditions: definition?.conditions,
        steps: states,
        transition: transition
      };

      console.log("Saving full workflow:", fullWorkflow);
      // Send fullWorkflow to backend here
    }, 100);
  };

  useEffect(() => {
    (window as any).setDefinitionData = (data: any) => setDefinition(data);
    (window as any).setWorkflowStates = (data: any[]) => setStates(data);
    (window as any).setWorkflowTransition = (data: any) => setTransition(data);
  }, []);

  const steps = [
    {
      id: 1,
      title: "Workflow Definition",
      component: <WorkflowDefinition />,
      buttons: [],
      showPrev: false,
      showNext: true,
    },
    {
      id: 2,
      title: "Workflow States",
      component: <WorkflowStates />,
      buttons: [],
      showPrev: true,
      showNext: true,
    },
    {
      id: 3,
      title: "Workflow Transition",
      component: <WorkflowTransitionLayout />,
      buttons: [
        {
          label: "Save",
          action: handleSaveAll,
        },
      ],
      showPrev: true,
      showNext: false,
    },
  ];

  return (
    <div className="flex gap-4 w-full h-full overflow-hidden">
      <div className="flex-1 flex flex-col gap-4">
        <div className="text-2xl font-bold text-blue-600">New Workflow</div>
        <Wizard steps={steps} />
      </div>
      <div className="w-[220px] bg-[#f9f9f9] p-2 border-l shadow-sm">
        <Sidebar />
      </div>
    </div>
  );
};

export default LayoutComponent;
