import { FormField } from "../types";

/**
 * Validate a single field based on the field schema.
 * @param field - The field schema.
 * @param value - The value of the field.
 * @param formData - The entire form data (for custom validation).
 * @returns Error message or undefined if the field is valid.
 */
export const validateField = (field: FormField, value: any, formData: Record<string, any>): string | undefined => {
  // Check conditionalRender. If the field shouldn't render, skip validation.
  if (field.conditionalRender && !field.conditionalRender(formData)) {
    return undefined; // Field is conditionally hidden, skip validation.
  }

  // Check required field
  if (field.required && (value === undefined || value === null || value === "")) {
    return `${field.label} is required.`;
  }

  // Check pattern validation
  if (field.validation?.pattern && !field.validation.pattern.test(value)) {
    return `${field.label} is invalid.`;
  }

  // Check minimum length
  if (field.validation?.minLength && value.length < field.validation.minLength) {
    return `${field.label} must be at least ${field.validation.minLength} characters.`;
  }

  // Check maximum length
  if (field.validation?.maxLength && value.length > field.validation.maxLength) {
    return `${field.label} must be at most ${field.validation.maxLength} characters.`;
  }

  // Check custom validation logic
  if (field.validation?.custom) {
    const customError = field.validation.custom(value, formData);
    if (customError) return customError;
  }

  return undefined; // No validation errors
};

/**
 * Validate the entire form.
 * @param fields - Array of all form fields.
 * @param formData - The form data to validate.
 * @returns An object with field names as keys and error messages as values.
 */
export const validateForm = (fields: FormField[], formData: Record<string, any>): Record<string, string> => {
  const errors: Record<string, string> = {};

  fields.forEach((field) => {
    const error = validateField(field, formData[field.name], formData);
    if (error) {
      errors[field.name] = error;
    }
  });

  return errors; // Return all validation errors
};
