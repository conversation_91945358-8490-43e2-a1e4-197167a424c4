import { ReactNode } from "react";
import Loader from "../loader/Loader";

type Props = {
  children?: ReactNode;
  type?: "submit" | "reset" | "button" | undefined;
  name?: string,
  value?: string | readonly string[] | number | undefined;
  form?: string;
  className?: string;
  loader?: boolean;
  disabled?: boolean | undefined;
  onClick?: React.FormEventHandler<HTMLButtonElement>;
};

const Button: React.FC<Props> = (props) => {
  let className: string = props.className || "px-4 py-1.5 space-links text-[12px] border border-primary rounded-md cursor-pointer hover:bg-primary hover:text-white";

  if (props.disabled) {
    className = `${className} cursor-default opacity-30`;
  }

  return (
    <button
      type={props.type}
      form={props.form}
      className={className}
      name={props.name}
      value={props.value}
      disabled={props.disabled}
      onClick={props.onClick}
    >
      {props.children}
      {props.loader && <Loader />}
    </button>
  )
}

export default Button;
