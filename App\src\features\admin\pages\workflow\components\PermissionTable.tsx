import React, { useState, useEffect, useCallback } from "react";
import { ColumnSchema } from "../../../../../shared/components/common/data-table/types";
import DataTable from "../../../../../shared/components/common/data-table/DataTable";
import { message, Modal } from "antd";
import { workflowService } from "../../../../../api/services/admin/workflowService";

interface Permission {
  key: string; 
  Id: string;  
  Type: string | string[]; // Updated to support both single and multiple types
  Name: string;
}

const PermissionTableSchema: ColumnSchema[] = [
  {
    key: "Id", 
    title: "ID",
    hidden: true,
    sorter: false,
    editable: false
  },
  {
    key: "Type",
    title: "Type",
    sorter: true,
    type: "select", // Changed from "select" to "multiselect"
    filterable: true,
    editable: true,
    options: [
      { value: "User", label: "User" },
      { value: "Role", label: "Role" },
      { value: "Group", label: "Group" },
      { value: "Admin", label: "Admin" },
      { value: "Editor", label: "Editor" }
    ],
  },
  {
    key: "Name",
    title: "Name",
    type: "text",
    filterable: true,
    editable: true
  },
];

const PermissionTable: React.FC = () => {
  const [data, setData] = useState<Permission[]>([]);
  const [editingId, setEditingId] = useState<string | null>(null);

  // Fetch permissions and ensure each item has both key and Id
  const fetchPermissions = useCallback(async () => {
    try {
      const result = await workflowService.fetchAllPermissions();
      if (result) {
        setData(result.Record.map((item: Omit<Permission, 'key'>) => ({
          ...item,
          key: item.Id, // Explicitly set key to match Id
          // Ensure Type is always an array for multiselect
          Type: Array.isArray(item.Type) ? item.Type : [item.Type].filter(Boolean)
        })));
      }
    } catch (error) {
      console.error("Failed to fetch permissions:", error);
      message.error("Failed to load permissions. Please try again.");
    }
  }, []);

  // Initial data load
  useEffect(() => {
    fetchPermissions();
  }, [fetchPermissions]);

  // Handle edit action
  const handleEditAction = (key: string) => {
    setEditingId(key);
  };

  // Handle saving (create/update)
  const handleSave = async (key: string, isEditMode: boolean, updatedData: Partial<Permission>) => {
    try {
      const effectiveKey = isEditMode ? editingId || key : key;
      
      // Prepare payload without the key property for API call
      const { key: _, ...apiPayload } = updatedData;
      
      // Convert Type array to string if single value, or keep as array
      const processedPayload = {
        ...apiPayload,
        Type: Array.isArray(apiPayload.Type) && apiPayload.Type.length === 1 
          ? apiPayload.Type[0] 
          : apiPayload.Type
      };
      
      let result: Permission | null;
      
      if (isEditMode) {
        result = await workflowService.updatePermission(effectiveKey, processedPayload);
      } else {
        result = await workflowService.createPermission(processedPayload);
      }

      if (result) {
        message.success(`Permission ${isEditMode ? 'updated' : 'created'} successfully`);
        setEditingId(null);
        fetchPermissions();
      }
    } catch (error) {
      console.error(`Failed to ${isEditMode ? 'update' : 'create'} permission:`, error);
      message.error(`Failed to ${isEditMode ? 'update' : 'create'} permission. Please try again.`);
    } 
  };

  // Handle deletion
  const handleDelete = async (key: string) => {
    Modal.confirm({
      title: 'Confirm Delete',
      content: 'Are you sure you want to delete this permission?',
      okText: 'Delete',
      cancelText: 'Cancel',
      onOk: async () => {
        try {
          const success = await workflowService.deletePermission(key);
          if (success) {
            message.success('Permission deleted successfully');
            fetchPermissions();
          }
        } catch (error) {
          console.error('Failed to delete permission:', error);
          message.error('Failed to delete permission. Please try again.');
        } 
      }
    });
  };

  return (
    <DataTable
      header="Permissions Management"
      data={data} 
      columnsSchema={PermissionTableSchema}
      inlineEditing={true}
      allowInlineAdd={true}
      onSave={handleSave} 
      onDelete={handleDelete}
      onEdit={handleEditAction}
    />
  );
};

export default PermissionTable;