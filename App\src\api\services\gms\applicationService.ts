import { IApplicationResponse, IApplicationData } from "../../../features/pre-award/pages/receipt/application/types";
import apiClient from "../../network/apiClient";


export const applicationService = {

    /** get the opportunity by id */
    async getById(id: number): Promise<IApplicationResponse> {
        const response = await apiClient.get<IApplicationResponse>(`${import.meta.env.VITE_PreAward_API_BASE_URL}/api/applications/${id}`);

        response.data.Template = response.data.Template.reduce((obj: Record<string, string>, curr: any) => {
            obj[curr.Language] = curr.FormTemplate;
            return obj;
        }, {});

        return response.data;
    },

    /** save the form data */
    async saveData(id: number, data: IApplicationData): Promise<IApplicationData> {
        const response = await apiClient.put<IApplicationData>(`${import.meta.env.VITE_PreAward_API_BASE_URL}/api/applications/${id}/data`, data);
        return response.data;
    }
}