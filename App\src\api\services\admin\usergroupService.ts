import apiClient from "../../network/apiClient";

// Define UserGroupMapping structure
export interface UserGroupMapping {
  Id: number;
  UserGroupsId: number;
  UserId: number;
  DefaultUserGroup: number;
}

// Define UserGroup structure
export interface UserGroup {
  Id: number;
  Code: string;
  Name: string;
  Email: string;
  UserGroupMapping?: UserGroupMapping[];
}

// Define fetch-all response structure
interface FetchResult {
  Record: UserGroup[];
  UserRights?: string;
}

const BASE_URL = `${import.meta.env.VITE_Admin_API_BASE_URL}/api/user-groups`;
const MAPPING_URL = `${import.meta.env.VITE_Admin_API_BASE_URL}/api/user-group-mapping`;

export const userGroupService = {
  // GET - Fetch all user groups
  async fetchAll(): Promise<FetchResult | null> {
    try {
      const response = await apiClient.get<FetchResult>(BASE_URL);
      return response.data ?? null;
    } catch (error) {
      console.error("Error fetching user groups:", error);
      return null;
    }
  },

  // GET - Fetch a single user group by ID (includes mappings)
  async getById(id: number): Promise<UserGroup> {
    try {
      const response = await apiClient.get<{ Record: UserGroup }>(`${BASE_URL}/${id}`);
      return response.data.Record ?? null;
    } catch (error) {
      console.error(`Error fetching user group with ID ${id}:`, error);
      throw error;
    }
  },

  // GET - Fetch mappings for a user group
  async getMappingsByGroupId(id: number): Promise<UserGroupMapping[]> {
    try {
      const response = await apiClient.get<{ Record: UserGroupMapping[] }>(`${MAPPING_URL}/${id}`);
      return response.data.Record ?? [];
    } catch (error) {
      console.error(`Error fetching mappings for group ${id}:`, error);
      return [];
    }
  },

  // POST - Create a new user group
  async create(userGroup: Partial<UserGroup>): Promise<UserGroup | null> {
    try {
      const response = await apiClient.post<UserGroup>(BASE_URL, userGroup);
      return response.data ?? null;
    } catch (error) {
      console.error("Error creating user group:", error);
      return null;
    }
  },

  // PUT - Update an existing user group
  async update(id: number, updatedGroup: Partial<UserGroup>): Promise<UserGroup | null> {
    try {
      const response = await apiClient.put<UserGroup>(`${BASE_URL}/${id}`, updatedGroup);
      return response.data ?? null;
    } catch (error) {
      console.error("Error updating user group:", error);
      return null;
    }
  },

  // PUT - Update mappings for a user group
  async updateMappings(id: number, mappings: UserGroupMapping[]): Promise<boolean> {
    try {
      await apiClient.put(`${BASE_URL}/${id}/user-group-mapping`, mappings);
      return true;
    } catch (error) {
      console.error(`Error updating mappings for user group ${id}:`, error);
      return false;
    }
  },

  // DELETE - Delete a user group
  async delete(id: number): Promise<boolean> {
    try {
      await apiClient.delete(`${BASE_URL}/${id}`);
      return true;
    } catch (error) {
      console.error("Error deleting user group:", error);
      return false;
    }
  }
};
