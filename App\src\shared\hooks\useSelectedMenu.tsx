import { useState, useEffect } from "react";
import { useMenu } from "../../providers/MenuProvider";
import { useLocation } from "react-router-dom";

const useSelectedMenu = () => {

    const [selectedMenu, setSelectedMenu] = useState('');
    const menus = useMenu();
    const location = useLocation();

    useEffect(() => {
        const menuPath = location.pathname.split('/').filter(Boolean).length >= 2? location.pathname.split('/').filter(Boolean)[2]:'';
        setSelectedMenu(menus?.Menu?.flatMap(x => x.Children).find(x => x.URL == menuPath)?.Name || '');
    }, [menus, location.pathname])

    return [ selectedMenu ];
}

export default useSelectedMenu;