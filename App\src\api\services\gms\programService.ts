import apiClient from "../../network/apiClient";

export const programService = {
    async getOptions(): Promise<any> {
        const response = await apiClient.get<any>(`${import.meta.env.VITE_Admin_API_BASE_URL}/api/programs/options`)
        return response.data;
    },

    async onBoard(programInfo: any) {
        const response = await apiClient.post(`${import.meta.env.VITE_Admin_API_BASE_URL}/api/programs`, programInfo);
        return response.data;
    }
}

