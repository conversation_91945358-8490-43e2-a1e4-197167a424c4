import { MessageOutlined } from "@ant-design/icons";
import styles from "./CommentPopup.module.css";

interface CommentIconProps {
  onClick: () => void;
}

const CommentIcon: React.FC<CommentIconProps> = ({ onClick }) => {
  return (
    <div onClick={onClick} className={styles.icon}>
      <MessageOutlined style={{ fontSize: "24px", cursor: "pointer" }} />
    </div>
  );
};

export default CommentIcon;
