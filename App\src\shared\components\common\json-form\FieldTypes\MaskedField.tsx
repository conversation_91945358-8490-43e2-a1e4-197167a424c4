import React, { useState } from "react";
import { FormField } from "../types";
import styles from "./styles.module.css";

interface MaskedFieldProps {
  field: FormField;
  value: string;
  onChange: (name: string, value: string) => void;
}

const MaskedField: React.FC<MaskedFieldProps> = ({ field, value, onChange }) => {
  const [maskedValue, setMaskedValue] = useState(value || "");

  const applyMask = (input: string, mask: string): string => {
    let masked = "";
    let inputIndex = 0;

    for (let i = 0; i < mask.length; i++) {
      if (mask[i] === "9") {
        if (input[inputIndex]) {
          masked += input[inputIndex];
          inputIndex++;
        } else {
          break;
        }
      } else {
        masked += mask[i];
      }
    }
    return masked;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const rawValue = e.target.value.replace(/\D/g, ""); // Remove non-numeric characters
    const formattedValue = applyMask(rawValue, field.mask || "************"); // Default phone mask
    setMaskedValue(formattedValue);
    onChange(field.name, formattedValue);
  };

  return (
    <div className={styles.maskedField}>
      <input
        type="text"
        id={field.name}
        name={field.name}
        value={maskedValue}
        className={styles.input}
        onChange={handleChange}
      />
    </div>
  );
};

export default MaskedField;
