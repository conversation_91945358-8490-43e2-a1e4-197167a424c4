import React from "react";
import { Outlet } from "react-router-dom";
import MainHeader from "../header/MainHeader";
import SidePanel from "../side-panel/SidePanel";
import Breadcrumbs from "../breadcrumbs/Breadcrumbs";
import MainFooter from "../footer/MainFooter";

const AdminLayout: React.FC = () => {
  return (
    <div className="min-h-screen flex">
      <SidePanel />
      <div className="flex-1 flex flex-col">
        <MainHeader />
        <main className="flex-1 p-4">
          {/* Display breadcrumbs below header */}
          <Breadcrumbs />

          <Outlet />
        </main>
        <MainFooter />
      </div>
    </div>
  );
};

export default AdminLayout;
