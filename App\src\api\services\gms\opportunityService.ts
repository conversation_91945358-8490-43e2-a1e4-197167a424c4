import { IOpportunityResponse, IOpportunityData } from "../../../features/pre-award/pages/receipt/opportunity/types";
import apiClient from "../../network/apiClient";


export const opportunityService = {

    /** get the opportunity by id */
    async getById(id: number): Promise<IOpportunityResponse> {
        const response = await apiClient.get<IOpportunityResponse>(`${import.meta.env.VITE_PreAward_API_BASE_URL}/api/opportunities/${id}`);

        response.data.Template = response.data.Template.reduce((obj: Record<string, string>, curr: any) => {
            obj[curr.Language] = curr.FormTemplate;
            return obj;
        }, {});

        return response.data;
    },

    /** save the form data */
    async saveData(id: number, data: IOpportunityData): Promise<IOpportunityData> {
        const response = await apiClient.put<IOpportunityData>(`${import.meta.env.VITE_PreAward_API_BASE_URL}/api/opportunities/${id}/data`, data);
        return response.data;
    }
}