import React, { useState, useEffect } from 'react';
import BasicInfo from './components/BasicInfo';
import ModelsInfo from './components/ModelsInfo';
import DataElements from './components/DataElements';
import RolesInfo from './components/RolesInfo';
import MenuInfo from './components/MenuInfo';
import Wizard from '../../../../../shared/components/render-elements/wizard/Wizard';
import { programService } from '../../../../../api/services/gms/programService';
import { toast } from 'react-toastify';
import { useNavigate } from 'react-router-dom';

type OptionItem = {
    value: string;
    label: string;
};

type MenuData = {
    [groupId: string]: {
        [menuText: string]: { checked: boolean; displayName: string }
    };
};

const ProgramForm: React.FC = () => {
    const [modelOptions, setModelOptions] = useState<OptionItem[]>([]);
    const [modulesData, setModulesData] = useState<OptionItem[]>([]);
    const [rolesData, setRolesData] = useState<OptionItem[]>([]);
    const [dataElementOptions, setDataElementOptions] = useState<OptionItem[]>([]);
    const navigate = useNavigate();

    const [basicInfo, setBasicInfo] = useState({ code: '', name: '', description: '' });
    const [modelsInfo, setModelsInfo] = useState<{ model: string; displayName: string }[]>([{ model: '', displayName: '' }]);
    const [dataElementRows, setDataElementRows] = useState<{ selected: OptionItem | null; displayName: string; description: string }[]>([{ selected: null, displayName: '', description: '' }]);
    const [roleRows, setRoleRows] = useState<{ selectedRole: string; selectedModules: string[] }[]>([{ selectedRole: '', selectedModules: [] }]);
    const [menuOptions, setMenuOptions] = useState<{ value: string; DisplayText: string; GroupId: string }[]>([]);
    const [menuOpenTab, setMenuOpenTab] = useState<string | null>(null);
    const [menuData, setMenuData] = useState<MenuData>({});

    const loadOptions = async () => {
        try {
            const response = await programService.getOptions();

            if (response.Models && Array.isArray(response.Models)) {
                const formattedModels = response.Models.map((model: any) => ({
                    value: model.Value?.toString() ?? "",
                    label: model.DisplayText ?? ""
                }));
                setModelOptions(formattedModels);
            }

            if (response.Modules && Array.isArray(response.Modules)) {
                const formattedModules = response.Modules.map((module: any) => ({
                    value: module.Value?.toString() ?? "",
                    label: module.DisplayText ?? ""
                }));
                setModulesData(formattedModules);
            }

            if (response.Roles && Array.isArray(response.Roles)) {
                const formattedRoles = response.Roles.map((role: any) => ({
                    value: role.Value?.toString() ?? "",
                    label: role.DisplayText ?? ""
                }));
                setRolesData(formattedRoles);
            }

            if (response.DataElements && Array.isArray(response.DataElements)) {
                const formattedDataElements: OptionItem[] = response.DataElements.map((item: any) => ({
                    value: item.Value?.toString() ?? "",
                    label: item.DisplayText ?? "",
                }));
                setDataElementOptions(formattedDataElements);
            }

            if (response.Menus && Array.isArray(response.Menus)) {
                const formattedMenus = response.Menus.map((menu: any) => ({
                    value: menu.Value?.toString() ?? '',
                    DisplayText: menu.DisplayText ?? '',
                    GroupId: menu.GroupId?.toString() ?? ''
                }));
                setMenuOptions(formattedMenus);
            }

        } catch (error) {
            console.error("Failed to load options:", error);
        }
    };

    useEffect(() => {
        loadOptions();
    }, []);

    const handleOnboardClick = async () => {
        try {
            const payload = {
                Code: basicInfo.code,
                Name: basicInfo.name,
                Description: basicInfo.description,
                ProgramModels: modelsInfo
                    .filter((model) => model.model)
                    .map((model) => {
                        const found = modelOptions.find((m) => m.value === model.model);
                        return {
                            ModelId: parseInt(model.model, 10),
                            DisplayName: found?.label ?? model.displayName,
                        };
                    }),
                ProgramDataElements: dataElementRows
                    .filter((row) => row.selected)
                    .map((row) => ({
                        DataElementId: parseInt(row.selected!.value, 10),
                        DataElement: row.selected!.label,
                        DisplayName: row.displayName,
                        Description: row.description,
                    })),
                ProgramRoles: roleRows
                    .filter((row) => row.selectedRole)
                    .map((row) => {
                        const selectedRole = rolesData.find(r => r.value === row.selectedRole);
                        const selectedModules = row.selectedModules
                            .map((mod) => modulesData.find((m) => m.value === mod))
                            .filter((mod): mod is { value: string; label: string } => !!mod);

                        return {
                            RoleId: selectedRole ? parseInt(selectedRole.value, 10) : 0,
                            RoleName: selectedRole?.label ?? "",
                            ModuleIds: selectedModules.map((mod) => parseInt(mod.value, 10)),
                            ModuleNames: selectedModules.map((mod) => mod.label),
                        };
                    }),
                ProgramMenus: Object.entries(menuData).flatMap(([groupId, items]) =>
                    Object.entries(items)
                        .filter(([_, val]) => val.checked)
                        .map(([menuText, val]) => {
                            const matchedMenu = menuOptions.find(
                                (menu) => menu.DisplayText === menuText && menu.GroupId === groupId
                            );

                            return {
                                MenuId: matchedMenu ? parseInt(matchedMenu.value, 10) : 0,
                                GroupId: parseInt(groupId, 10),
                                MenuText: menuText,
                                DisplayName: val.displayName || menuText,
                            };
                        })
                ),
            };

            const response = await programService.onBoard(payload);
            toast.success('Program onboarded successfully!');
            console.log("Onboard Click Data", response);
            setTimeout(() => {
                navigate('/admin/application/programs');
            }, 1000);
        } catch (error) {
            console.error("Onboard failed:", error);
            toast.error('Failed to onboard program.');
        }
    };

    const schema = {
        steps: [
            {
                id: 1,
                title: "Program Info",
                component: <BasicInfo basicInfo={basicInfo} setBasicInfo={setBasicInfo} />,
                showNext: true,
            },
            {
                id: 2,
                title: "Program Models",
                component: <ModelsInfo modelOptions={modelOptions} modelsInfo={modelsInfo} setModelsInfo={setModelsInfo} />,
                showPrev: true,
                showNext: true,
            },
            {
                id: 3,
                title: "Program Data Elements",
                component: <DataElements dataElementOptions={dataElementOptions} dataElementRows={dataElementRows} setDataElementRows={setDataElementRows} />,
                showPrev: true,
                showNext: true,
            },
            {
                id: 4,
                title: "Program Roles",
                component: <RolesInfo rolesOptions={rolesData} moduleOptions={modulesData} rows={roleRows} setRows={setRoleRows} />,
                showPrev: true,
                showNext: true,
            },
            {
                id: 5,
                title: "Program Menu",
                component: <MenuInfo moduleOptions={modulesData} menuItems={menuOptions} openTab={menuOpenTab} setOpenTab={setMenuOpenTab} menuData={menuData} setMenuData={setMenuData} />,
                showPrev: true,
                buttons: [
                    {
                        label: "Onboard",
                        action: handleOnboardClick,
                    },
                ],
            },
        ],
    };

    return (
        <>
            <Wizard steps={schema.steps} />
        </>
    );
};

export default ProgramForm;