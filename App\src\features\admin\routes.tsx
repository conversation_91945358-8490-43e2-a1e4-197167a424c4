import { lazy } from "react";
import { RouteObject } from "react-router-dom";
import UserGroupForm from "./pages/usergroups/UserGroupForm";
import BusinessUnitList from "./pages/businessunit/BusinessUnitList";
import LookUpTypeList from "./pages/lookup/LookUpTypeList";
import LookUpTypeForm from "./pages/lookup/LookUpTypeForm";

const AdminLayout = lazy(() => import("./AdminLayout"));
const ModuleGroup = lazy(() => import("../dashboard/pages/ModuleGroup"));
const ModuleLayout = lazy(() => import("../../shared/components/render-elements/layouts/ModuleLayout"))
// Role pages
const RoleList = lazy(() => import("./pages/role/RoleList"));
// User pages
const UserList = lazy(() => import("./pages/system/user/UserList"));
const UserForm = lazy(() => import("./pages/system/user/UserForm"));
// User group pages
const UserGroupList = lazy(() => import("./pages/usergroups/UserGroupList"));
//Permission pages
const PermissionList = lazy(() => import("./pages/permission/PermissionList"));
const PermissionForm = lazy(() => import("./pages/permission/PermissionForm"));

// Program pages
const ProgramList = lazy(() => import("./pages/application/program/ProgramList"));
const ProgramForm = lazy(() => import("./pages/application/program/ProgramForm"));
// Country pages
const CountryList = lazy(() => import("./pages/application/country/CountryList"));
const CountryForm = lazy(() => import("./pages/application/country/CountryForm"));
// Event pages
const EventList = lazy(() => import("./pages/application/event/EventList"));
const EventForm = lazy(() => import("./pages/application/event/EventForm"));
// Fiscal Year pages
const FiscalYearList = lazy(() => import("./pages/application/fiscal-year/FiscalYearList"));
const FiscalYearForm = lazy(() => import("./pages/application/fiscal-year/FiscalYearForm"));
// Framework pages
const FrameworkList = lazy(() => import("./pages/application/framework/FrameworkList"));
const FrameworkForm = lazy(() => import("./pages/application/framework/FrameworkForm"));
// Workflow pages
const WorkflowList = lazy(() => import("./pages/workflow/WorkflowList"));
const WorkflowLayout = lazy(() => import("./pages/workflow/layout/WorkflowLayout"));
// Always define nested paths without a leading slash (`/`)
export const adminRoutes: RouteObject[] = [
  {
    path: "/admin",
    element: <AdminLayout />,
    handle: { breadcrumb: "System Administration" },
    children: [
      { index: true, element: <ModuleGroup />, handle: { breadcrumb: "Modules" } },
      {
        path: "system",
        element: <ModuleLayout />,
        children: [
          { index: true, element: <UserList /> },
          {
            path: "users",
            children: [
              { index: true, element: <UserList /> },
              { path: "create", element: <UserForm mode="create" /> },
              { path: ":id/edit", element: <UserForm mode="edit" />, handle: { breadcrumb: "Edit" } },
            ],
          },
          {
            path: "roles",
            children: [
              { index: true, element: <RoleList /> },
            ],
          },
          {
            path: "businessunits",
            children: [
              { index: true, element: <BusinessUnitList /> },
            ],
          },
          {
            path: "lookuptypes",
            children: [
              { index: true, element: <LookUpTypeList /> },
              { path: "create", element: <LookUpTypeForm mode="create" /> },
              {
                path: ":id/edit",
                element: <LookUpTypeForm mode="edit" />,
                handle: {
                  breadcrumb: ({ params }: { params: { id: string } }) => `Edit Group #${params.id}`
                }
              },
            ],
          },
          {
            path: "permissions",
            children: [
              { index: true, element: <PermissionList /> },
              { path: "create", element: <PermissionForm mode="create" /> },
              {
                path: ":id/edit",
                element: <PermissionForm mode="edit" />,
                handle: { breadcrumb: "Edit" }
              },
            ],
          },
          {
            path: "usergroups",
            handle: { breadcrumb: "User Groups" },
            children: [
              { index: true, element: <UserGroupList /> },
              { path: "create", element: <UserGroupForm mode="create" /> },
              {
                path: ":id/edit",
                element: <UserGroupForm mode="edit" />,
                handle: {
                  breadcrumb: ({ params }: { params: { id: string } }) => `Edit Group #${params.id}`
                }
              },
            ],
          },
          {
            path: "workflows",
            handle: { breadcrumb: "Workflows" },
            children: [
              { index: true, element: <WorkflowList /> },
              { path: "create", element: <WorkflowLayout />, handle: { menuPanel: false } },           
            ],
          }
        ]
      },
      {
        path: "application",
        element: <ModuleLayout />,
        children: [
          { index: true, element: <ProgramList /> },
          {
            path: "programs",
            children: [
              { index: true, element: <ProgramList /> },
              { path: "create", element: <ProgramForm/>, handle: { menuPanel: false } },
            ]
          },
          {
            path: "countries",
            children: [
              { index: true, element: <CountryList /> },
              { path: "create", element: <CountryForm mode="create" /> },
              {
                path: ":id/edit",
                element: <CountryForm mode="edit" />,
                handle: {
                  breadcrumb: ({ params }: { params: { id: string } }) => `Edit #${params.id}`
                }
              },
            ],
          },
          {
            path: "events",
            children: [
              { index: true, element: <EventList /> },
              { path: "create", element: <EventForm mode="create" />},
              {
                path: ":id/edit",
                element: <EventForm mode="edit" />,
                handle: {
                  breadcrumb: ({ params }: { params: { id: string } }) => `Edit #${params.id}`
                }
              },
            ],
          },
          {
            path: "fiscal-years",
            children: [
              { index: true, element: <FiscalYearList /> },
              { path: "create", element: <FiscalYearForm mode="create" /> },
              {
                path: ":id/edit",
                element: <FiscalYearForm mode="edit" />,
                handle: {
                  breadcrumb: ({ params }: { params: { id: string } }) => `Edit #${params.id}`
                }
              },
            ],
          },
          {
            path: "frameworks",
            children: [
              { index: true, element: <FrameworkList /> },
              { path: "create", element: <FrameworkForm mode="create" /> },
              {
                path: ":id/edit",
                element: <FrameworkForm mode="edit" />,
                handle: {
                  breadcrumb: ({ params }: { params: { id: string } }) => `Edit #${params.id}`
                }
              },
            ],
          }
        ]
      }
    ],
  }
];
