import React from 'react';
import { Select } from 'antd';

import styles from "../styles.module.css";


interface BuilderHeaderProps {
    title: string;
    language?: string;
    onLanguageChange: (language: string) => void   
}

const BuilderHeader: React.FC<BuilderHeaderProps> = ({
    title,
    language,
    onLanguageChange
}) => {

    return (<div className={`w-full flex p-5 ${styles.builderHeader}`} >
        <span className='w-[70%]'>{title}</span>
        <span className='w-[25%]'>
            <Select defaultValue={language}
                placeholder={"Search to Select"}
                optionFilterProp="children"
                options={[]}
                onChange={(e) => {
                    onLanguageChange(e);
                }} 
            /> 
        </span>
    </div>)
}

export default BuilderHeader;