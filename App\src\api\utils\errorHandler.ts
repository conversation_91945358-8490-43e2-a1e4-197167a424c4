import axios, { AxiosError } from "axios";

// Define a generic interface for error responses
export interface GenericErrorResponse<T = { message: string }> {
  status: number; // HTTP status code
  data: T;        // Response data (can include validation errors)
  headers: Record<string, any>; // Response headers
  isNetworkError: boolean; // Whether the error is related to networking
  validationErrors?: Record<string, string>; // Optional validation errors
}

// Centralized error handler function
export const handleError = <T = { message: string }>(error: unknown): GenericErrorResponse<T> => {
  if (axios.isAxiosError(error)) {
    const axiosError = error as AxiosError<T>;

    // Extract validation errors if available (e.g., status 422 for validation issues)
    const validationErrors = axiosError.response?.status === 422
      ? (axiosError.response?.data as any)?.validationErrors // Adjust this to match your API's error structure
      : axiosError.response?.status === 400
      ? (axiosError.response?.data as any)?.errors
      : undefined;

    return {
      status: axiosError.response?.status || 500,
      data: axiosError.response?.data || ({ message: "An unexpected error occurred." } as T),
      headers: axiosError.response?.headers || {},
      isNetworkError: !axiosError.response,
      validationErrors, // Include validation errors if available
    };
  }

  // Handle non-Axios errors (e.g., runtime or unexpected errors)
  return {
    status: 500,
    data: { message: "An unexpected error occurred." } as T,
    headers: {},
    isNetworkError: false,
    validationErrors: undefined, // No validation errors for non-Axios errors
  };
};
