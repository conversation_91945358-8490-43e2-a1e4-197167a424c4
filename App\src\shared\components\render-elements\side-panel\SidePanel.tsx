import React, { useState } from "react";
import HomeIcon from "./components/HomeIcon";
import SettingsIcon from "./components/SettingsIcon";
import { useNavigate, useLocation } from 'react-router-dom';

const SidePanel: React.FC = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isMobileVisible, setIsMobileVisible] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // Function to handle toggling both mobile visibility and expansion
  const toggleSidePanel = () => {
    setIsMobileVisible((prev) => !prev);
    setIsExpanded((prev) => !prev);
  };

  const handleHomeClick = () => {
    navigate('/');
  };

  const handleSettingsClick = () => {
    navigate('/settings');
  };

  const isDashboardActive = location.pathname === '/';
  const isSettingsActive = location.pathname === '/settings';

  return (
    <>
      {/* Toggle Button (Always Visible) */}
      <button
        className="fixed top-4 left-4 z-10 bg-gray-700 text-white px-2 py-1 rounded md:hidden"
        onClick={toggleSidePanel}
      >
        {isExpanded ? "✖" : "☰"}
      </button>

      {/* Side Panel */}
      <div
        className={`transition-all duration-300 bg-[#0b233f] text-white
          ${isExpanded ? "w-52" : "w-16"} h-screen
          md:sticky md:top-0 md:left-0 md:w-52
          ${isMobileVisible ? "absolute z-10 left-0 w-52" : "absolute -left-52 w-0"} md:w-auto`}
      >
        <div className="h-[60px] px-5 py-[10px] flex items-center bg-[#00AEEF]">
          <button
            className={`bg-[#00AEEF] w-8 h-8 border border-white py-1 rounded flex items-center justify-center ${isExpanded ? 'px-[10px]' : 'px-[8px]'}`}
            onClick={toggleSidePanel}
          >
            {isExpanded ? "X" : "☰"}
          </button>
          {isExpanded && (
            <span className="ml-[15px] text-[13px] font-bold">
              MENU
            </span>
          )}
        </div>

        {/* Navigation Menu */}
        <nav className="mt-12">
          <ul>
            <li
              className={`relative flex items-center w-full py-2 border-b border-gray-700 px-4 cursor-pointer
              ${isDashboardActive ? 'bg-[#d5e7f1] before:content-[""] before:absolute before:left-0 before:top-0 before:bottom-0 before:w-[5px] before:bg-[#0b233f]' : ''}`}
              onClick={handleHomeClick}
            >
              <span className="px-2 py-1"><HomeIcon isActive={isDashboardActive} /></span>
              {isExpanded && <span className={`font-bold text-[13px] leading-[15px] ${isDashboardActive ? 'text-[#0b233f]' : 'text-[#ffffff]'}`}>
                Dashboard
              </span>}
            </li>
            <li
              className={`relative flex items-center w-full py-2 border-b border-gray-700 px-4 cursor-pointer
              ${isSettingsActive ? 'bg-[#d5e7f1] before:content-[""] before:absolute before:left-0 before:top-0 before:bottom-0 before:w-[5px] before:bg-[#0b233f]' : ''}`}
              onClick={handleSettingsClick}
            >
              <span className="px-2 py-1"><SettingsIcon isActive={isSettingsActive} /></span>
              {isExpanded &&
                <span className={`font-bold text-[13px] leading-[15px] ${isSettingsActive ? 'text-[#0b233f]' : 'text-[#ffffff]'}`}>
                  Admin Settings
                </span>}
            </li>
          </ul>
        </nav>
      </div>
    </>
  );
};

export default SidePanel;
