import React from "react";
import { ColumnSchema } from "../../../../../shared/components/common/data-table/types";
import { MasterPage } from "../../../../../shared/components/render-elements";

const userSchema: ColumnSchema[] = [
  {
    key: "Id",
    title: "Id",
    hidden: true,
    sorter: false
  },
  {
    key: "UserName",
    title: "Username",
    sorter: true,
    type: "text",
    filterable: true
  },
  {
    key: "LoginName",
    title: "Login Name",
    type: "text",
    filterable: true
  },
  {
    key: "Email",
    title: "Email",
    type: "text",
    filterable: true
  },
  {
    key: "RoleId",
    title: "Role",
    type: "select",
    filterable: true
  },
  {
    key: "EffectiveFrom",
    title: "Effective From",
    type: "date",
    filterable: true
  },
  {
    key: "EffectiveTo",
    title: "Effective To",
    type: "date",
    filterable: true
  },
  {
    key: "IsActive",
    title: "Active",
    type: "select",
    options: [
      { label: "Yes", value: 1 },
      { label: "No", value: 0 }
    ],
    filterable: true
  },
];

const UserList: React.FC = () => {
  return <MasterPage
    entityName="User"
    columnsSchema={userSchema}
    mode="form"
    apiEndpoints={{
      list: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/users`,
      delete: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/users`
    }}
    routeTemplates={{
      create: "/admin/system/users/create",
      edit: "/admin/system/users/:id/edit",
    }}
  />;
};

export default UserList;
