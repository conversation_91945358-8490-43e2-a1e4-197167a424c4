import axiosInstance from "../utils/axiosInstance";
import { AxiosRequestConfig } from "axios";
import { handleError } from "../utils/errorHandler";

type ApiResponse<T> = {
  status: number;
  data: T;
  headers: any;
};

const apiClient = {
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await axiosInstance.get<T>(url, config);
      return { status: response.status, data: response.data, headers: response.headers };
    } catch (error) {
      throw handleError(error);
    }
  },

  async post<T>(url: string, payload: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await axiosInstance.post<T>(url, payload, config);
      return { status: response.status, data: response.data, headers: response.headers };
    } catch (error) {
      throw handleError(error);
    }
  },

  async put<T>(url: string, payload: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await axiosInstance.put<T>(url, payload, config);
      return { status: response.status, data: response.data, headers: response.headers };
    } catch (error) {
      throw handleError(error);
    }
  },

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await axiosInstance.delete<T>(url, config);
      return { status: response.status, data: response.data, headers: response.headers };
    } catch (error) {
      throw handleError(error);
    }
  },

  async patch<T>(url: string, payload: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await axiosInstance.patch<T>(url, payload, config);
      return { status: response.status, data: response.data, headers: response.headers };
    } catch (error) {
      throw handleError(error);
    }
  },
};

export default apiClient;
