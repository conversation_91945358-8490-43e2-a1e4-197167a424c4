
export interface Workflow {
    name: string;
    description: string;
    action: string;
    table: string;
    conditions: any[];
    steps: Step[];
    transition: Transition;
  }
  
  export interface Step {
    stepCode: string;
    stepName: string;
    action: string;
    stepNo: number;
  }
  
  export interface Transition {
    from: string;
    to: string;
    rules: {
      ruleConditions: string;
      ruleValidations: string;
      ruleDataCollection: string;
    };
    activities: {
      notificationType: string;
      Email: string;
    };
    permissions: any[];
  }
  