import React from "react";
import { FormField } from "../types";
import styles from "./styles.module.css";

interface RatingFieldProps {
  field: FormField;
  value: number;
  onChange: (name: string, value: number) => void;
  maxRating?: number; // Maximum number of stars
}

const RatingField: React.FC<RatingFieldProps> = ({ field, value, onChange, maxRating = 5 }) => {
  return (
    <div className={styles.ratingField}>
      <div className={styles.stars}>
        {[...Array(maxRating)].map((_, index) => {
          const ratingValue = index + 1;
          return (
            <span
              key={ratingValue}
              className={`${styles.star} ${ratingValue <= value ? styles.selected : ""}`}
              onClick={() => onChange(field.name, ratingValue)}
            >
              ★
            </span>
          );
        })}
      </div>
    </div>
  );
};

export default RatingField;
