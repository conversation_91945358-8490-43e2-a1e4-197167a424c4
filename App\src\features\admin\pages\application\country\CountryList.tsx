import React from "react";
import { MasterPage } from "../../../../../shared/components/render-elements";
import { ColumnSchema } from "../../../../../shared/components/common/data-table";

const countrySchema: ColumnSchema[] = [
  {
    key: "Id",
    title: "Id",
    hidden: true,
    editable: false,
    sorter: false
  },
  {
    key: "Code",
    title: "Code",
    editable: true,
    sorter: true,
    type: "text",
    filterable: true
  },
  {
    key: "Description",
    title: "Description",
    editable: true,
    sorter: true,
    type: "text",
    filterable: true
  },
  {
    key: "Active",
    title: "Active",
    editable: true,
    sorter: true,
    type: "select",
    options: [
      { label: "Yes", value: 1 },
      { label: "No", value: 0 }
    ],
    filterable: true
  }
];

const CountryList: React.FC = () => {
  return <MasterPage
    entityName="Country"
    columnsSchema={countrySchema}
    mode="form"
    apiEndpoints={{
      create: `${import.meta.env.VITE_PGM_API_BASE_URL}/api/country`,
      edit: `${import.meta.env.VITE_PGM_API_BASE_URL}/api/country`,
      delete: `${import.meta.env.VITE_PGM_API_BASE_URL}/api/country`,
      list: `${import.meta.env.VITE_PGM_API_BASE_URL}/api/country`
    }}
    routeTemplates={{
      create: "/admin/application/countries/create",
      edit: "/admin/application/countries/:id/edit",
    }}
  />;
}

export default CountryList;
