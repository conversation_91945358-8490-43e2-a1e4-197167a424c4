import React from 'react';
import { JForm } from '../../json-form';

interface FormProps {
    formSchema: any;  
    formData: Record<string, any>;
    setFormData: (data: Record<string, any>) => void
}

const FormContainer: React.FC<FormProps> = ({
    formSchema,
    formData,
    setFormData
}) => {

    return <div className='p-[20px]'>
        <JForm 
        schema={{...formSchema, actions: { showActions: false }}} 
        data={formData}
        onFormChange={setFormData}
        onSubmit={() => {}} 
        />
    </div>;
}

export default FormContainer;
