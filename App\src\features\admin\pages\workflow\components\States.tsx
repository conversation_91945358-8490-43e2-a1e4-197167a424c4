
import React, { useEffect, useRef } from "react";
import { ColumnSchema } from "../../../../../shared/components/common/data-table/types";
import { MasterPage } from "../../../../../shared/components/render-elements";

const workflowStatesSchema: ColumnSchema[] = [
  { key: "Id", title: "Id", hidden: true, sorter: false },
  { key: "WorkflowDefinitionId", title: "Workflow Definition Id",hidden: true, editable: true, type: "number" },
  { key: "StepNo", title: "Step No", editable: true, type: "number" },
  { key: "StepCode", title: "Step Code", editable: true, type: "text" },
  { key: "StepName", title: "Step Name", editable: true, type: "text" },
  { key: "Action", title: "Action", editable: true, type: "text" }
];

const WorkflowStates: React.FC = () => {
  const pageRef = useRef<any>(null);

  useEffect(() => {
    (window as any).saveWorkflowStates = async () => {
      const rows = await pageRef.current?.getData?.(); // assume getData returns table data
      (window as any).setWorkflowStates?.(rows || []);
    };
  }, []);

  return <MasterPage
    entityName="Workflow States / Stages"
    columnsSchema={workflowStatesSchema}
    mode="inline"
    apiEndpoints={{
      list: "/api/workflow-step",
      create: "/api/workflow-step",
      edit: "/api/workflow-step",
      delete: "/api/workflow-step"
    }}
  />;
};

export default WorkflowStates;
