.autoCompleteField {
  position: relative;
  width: 100%;
}

.input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 16px;
  outline: none;
}

.input:focus {
  border-color: #007bff;
}

.suggestionsList {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background: white;
  border: 1px solid #ccc;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.suggestionsList li {
  padding: 5px;
  cursor: pointer;
  font-size: 16px;
}

.suggestionsList li:hover {
  background-color: #f0f0f0;
}

.suggestionsList li:active {
  background-color: #d0d0d0;
}

.multiSelectField {
  position: relative;
  width: 100%;
}

.multiSelectField input[type="text"] {
  border: none !important;
  min-width: 50px;
  max-width: 100%;
  box-shadow: none !important;
}

.inputContainer {
  display: flex;
  align-items: center;
  border: 1px solid #ccc;
  padding: 0 2px 0 3px;
  border-radius: 4px;
  cursor: pointer;
}

.inputContainer:focus-within {
  /* Creates a sharp blue ring */
  outline: 1px solid rgba(0, 123, 255, 0.8);
  /* Smooth animation */
  transition: box-shadow 0.3s ease, outline 0.3s ease;
}

.selectedChips {
  display: flex;
  /* flex-wrap: wrap; */
  gap: 3px;
}

.chip {
  background-color: #007bff;
  color: white;
  padding: 2px 5px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  width: auto;
  white-space: nowrap;
}

.chip button {
  background: none;
  border: none;
  color: white;
  margin-left: 5px;
  cursor: pointer;
}

.toggleField {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toggleField .switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
  margin: auto 0;
}

.toggleField .switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggleField .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  border-radius: 20px;
  transition: 0.4s;
}

.toggleField input:checked+.slider {
  background-color: #007bff;
}

.toggleField .slider::before {
  position: absolute;
  content: "";
  height: 14px;
  width: 14px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  border-radius: 50%;
  transition: 0.4s;
}

.toggleField input:checked+.slider::before {
  transform: translateX(20px);
}

.ratingField {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.ratingField .label {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 6px;
}

.ratingField .stars {
  display: flex;
  gap: 5px;
}

.ratingField .star {
  font-size: 24px;
  cursor: pointer;
  color: #ccc;
  transition: color 0.3s;
}

.ratingField .star.selected {
  color: #f5a623;
}

.ratingField .star:hover {
  color: #ffcc00;
}

.fileField {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.fileField .uploadLabel {
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  cursor: pointer;
  padding: 5px;
  margin: 0;
  background-color: #f1f1f1;
  border: 1px solid #ccc;
  border-radius: 4px;
  transition: background 0.3s;
}

.fileField .uploadLabel:hover {
  background-color: #e1e1e1;
}

.fileField .hiddenInput {
  display: none;
}

.fileField .attachIcon {
  cursor: pointer;
}

.fileField .fileList {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.fileField .fileList li {
  display: flex;
  gap: 10px;
  align-items: baseline;
}

.fileField .fileList li span {
  display: flex;
  align-items: center;
  padding: 0;
}

.fileField .fileList li .removeButton {
  width: 16px;
  height: 16px;
  line-height: 14px;
  border-radius: 50%;
  border: none;
  background-color: #ff4d4d;
  color: white;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  justify-content: center;
  transition: all 0.3s ease-in-out;
}

.fileField .error {
  color: red;
}

.treeDropdown {
  position: relative;
}

.treeDropdownSelected {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 5px;
  cursor: pointer;
  background: #fff;
  transition: background 0.2s ease-in-out;
  overflow: hidden;
}

.treeDropdownSelected:focus-within {
  outline: 1px solid rgba(0, 123, 255, 0.8);
  transition: box-shadow 0.3s ease, outline 0.3s ease;
}

.treeDropdownMenu {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background: white;
  border: 1px solid #ccc;
  padding: 8px 8px 8px 0;
  z-index: 100;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.2s ease-in-out;
}

.treeNode {
  margin-left: 8px;
}

.treeItem {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 2px;
}

.treeItem:hover {
  background: #f4f4f4;
}

.toggleIcon {
  margin-right: 5px;
  font-size: 10px;
  cursor: pointer;
}

.treeLabel {
  padding: 0 2px;
  cursor: pointer;
}

.treeChildren {
  margin-left: 16px;
}
