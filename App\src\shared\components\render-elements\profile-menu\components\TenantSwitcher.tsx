import React, { useState, useEffect } from "react";
import { useMenu } from "../../../../../providers/MenuProvider";
import { TenantItem } from "../../../../../api/services/common/appSettingService";

const TenantSwitcher: React.FC = () => {
  const modules = useMenu();
  const [tenants, setTenants] = useState<TenantItem[]>(modules?.Tenants ?? []);
  const [selectedTenant, setSelectedTenant] = useState<string | number>("");
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    const defaultTenant = localStorage.getItem("tenant") || tenants[0]?.Value;
    setSelectedTenant(defaultTenant);
    localStorage.setItem("tenant", defaultTenant.toString());
    setTenants(modules?.Tenants ?? []);
  }, [tenants]);

  const handleTenantChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    event.preventDefault();

    const newTenant = event.target.value;
    setSelectedTenant(newTenant);
    localStorage.setItem("tenant", newTenant);
    setIsOpen(false);
    window.location.reload();
  };

  return (
    <div className="relative bg-gray-100 p-2 border-t border-gray-300 text-sm">
      {!isOpen ? (
        // Label with Tenant Name & Switch Icon
        <div
          className="flex items-center justify-between cursor-pointer p-2 rounded-md hover:bg-white"
          onClick={() => setIsOpen(true)}
        >
          <div className="flex flex-col font-light">
            <span>Tenant</span>
            <span className="font-semibold text-gray-700 truncate">
              {tenants.find(t => t.Value == selectedTenant)?.DisplayText || "Unknown Tenant"}
            </span>
          </div>
          {/* SVG Icon for Switching */}
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="1"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="w-5 h-5 text-gray-600"
          >
            <path d="M5 12h14" />
            <path d="M10 6l-5 6 5 6" />
            <path d="M14 6l5 6-5 6" />
          </svg>

        </div>
      ) : (
        // Radio Select Dropdown (Placed in Label Position)
        <div className="bg-white rounded-md p-3 max-h-40 overflow-y-auto">
          {tenants.map((tenant) => (
            <label key={tenant.Value} className="flex items-center gap-2 p-2">
              <input
                type="radio"
                name="tenant"
                value={tenant.Value}
                checked={String(selectedTenant) == tenant.Value}
                onChange={handleTenantChange}
              />
              {tenant.DisplayText}
            </label>
          ))}
          {/* Close Button */}
          <button
            className="mt-3 p-2 w-full text-center text-gray-600 bg-gray-100 rounded-md"
            onClick={() => setIsOpen(false)}
          >
            Close
          </button>
        </div>
      )}
    </div>
  );
};

export default TenantSwitcher;
