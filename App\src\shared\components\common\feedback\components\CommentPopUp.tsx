import styles from "./CommentPopup.module.css";
import Button from "../../button/Button";
import ResolveButton from "./ResolveButton";

interface Comment {
  Comment: string | null;
  Resolved: boolean;
  FeedbackHeaderId: number;
  RowID: number;
  RowKey: string;
  Section: string;
  SectionPart: string;
  Count: number;
  AllCount: number;
}


interface CommentPopupProps {
  statusOpen: boolean;
  toggleStatus: () => void;
  chatList: Comment[];
  commentText: string;
  setCommentText: (text: string) => void;
  onSubmit: () => void;
  onCancel: () => void;
}

const CommentPopup: React.FC<CommentPopupProps> = ({
  statusOpen,
  toggleStatus,
  chatList,
  commentText,
  setCommentText,
  onSubmit,
  onCancel,
}) => {
  return (
    <div className={styles.popup}>
      <div className={styles.header}>
        <span>Comment</span>
        <ResolveButton statusOpen={statusOpen} toggleStatus={toggleStatus} />
      </div>

      <div className={styles.status}>
        Status: <span className={styles.statusValue}>{statusOpen ? "Open" : "Closed"}</span>
      </div>

      <div className={styles.chatList}>
        {chatList.length > 0 ? (
          chatList.map((chat, index) => (
            <div key={index} className={styles.chatItem}>
              {chat.Comment || "(No comment)"}
            </div>
          ))
        ) : (
          <div className={styles.noComments}>No comments available.</div>
        )}
      </div>

      {statusOpen && (
        <div className={styles.inputArea}>
          <textarea
            value={commentText}
            onChange={(e) => setCommentText(e.target.value)}
            placeholder="Enter your comment"
            className={styles.textarea}
          />
          <div className={styles.buttonGroup}>
            <Button type="button" onClick={onSubmit}>Submit</Button>
            <Button type="button" onClick={onCancel}>Cancel</Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default CommentPopup;
