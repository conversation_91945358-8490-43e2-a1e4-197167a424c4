import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import ModuleCard from "../components/ModuleCard";
import { Module } from "../../../api/services/common/appSettingService";
import { useMenu } from "../../../providers/MenuProvider";

const ModuleGroup: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const data = useMenu();

  const [moduleItems, setModuleItems] = useState<Module[]>([]);

  useEffect(() => {
    const moduleGroup = data?.ModuleGroup?.find((item) => item?.URL === location.pathname);
    const modules = data?.Modules?.filter((item) => item?.ModuleGroupId === moduleGroup?.Id);
    setModuleItems(modules ?? []);
  }, [data]);

  return (
    <div className="flex flex-wrap justify-center gap-6 p-6">
      {moduleItems.map((mod) => (
        <ModuleCard
          key={mod.ModuleId}
          module={mod}
          onSelect={() => { navigate(`${mod.SourceUrl}`) }}
        >
        </ModuleCard>
      ))}
    </div>
  )
};

export default ModuleGroup;
