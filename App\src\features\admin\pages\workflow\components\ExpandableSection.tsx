import React, { useState } from "react";
import { PlusOutlined, MinusOutlined, RightOutlined } from "@ant-design/icons";
import { Button } from "antd";

interface ExpandableSectionProps {
  title: string;
  children: React.ReactNode;
  onAddClick?: () => void;
  showAdd?: boolean;
  count?: number;
  level?: number;
}

const ExpandableSection: React.FC<ExpandableSectionProps> = ({
  title,
  children,
  onAddClick,
  showAdd = true,
  count = 0,
  level = 0,
}) => {
  const [expanded, setExpanded] = useState(false); // Changed to default false
  const hasChildren = React.Children.count(children) > 0;

  return (
    <div className={`relative ${level > 0 ? "pl-6" : ""}`}>
      {/* Vertical line for child sections */}
      {level > 0 && (
        <div className="absolute left-3 top-5 bottom-0 w-px bg-gray-300"></div>
      )}

      {/* Section Header */}
      <div
        className={`flex justify-between items-center py-2 pr-2 rounded-md cursor-pointer transition ${
          expanded ? "bg-gray-50" : "hover:bg-gray-50"
        }`}
        onClick={() => hasChildren && setExpanded((prev) => !prev)}
      >
        <div className="flex items-center space-x-2">
          {hasChildren ? (
            <span className="text-gray-400 w-4 flex justify-center">
              {expanded ? (
                <MinusOutlined className="text-xs" />
              ) : (
                <RightOutlined className="text-xs" />
              )}
            </span>
          ) : (
            <span className="w-4"></span>
          )}
          <span className="font-medium text-gray-800">{title}</span>
          {count > 0 && (
            <span className="text-xs text-gray-500">({count})</span>
          )}
        </div>

        {showAdd && (
          <Button
            type="text"
            size="small"
            icon={<PlusOutlined className="text-xs" />}
            onClick={(e) => {
              e.stopPropagation();
              onAddClick?.();
            }}
            className="text-gray-500 hover:text-gray-700"
          />
        )}
      </div>

      {/* Child Content */}
      {expanded && hasChildren && (
        <div className="ml-4 pl-2 border-l border-gray-200">
          {React.Children.map(children, (child, index) => (
            <div key={index} className="relative">
              {/* Horizontal connector for first child */}
              {index === 0 && level > 0 && (
                <div className="absolute left-0 top-0 h-px w-4 bg-gray-300"></div>
              )}
              {React.isValidElement(child)
                ? React.cloneElement(child as React.ReactElement<any>, {
                    level: level + 1,
                    ...(child.props as object),
                  })
                : child}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ExpandableSection;