import { ReactNode } from "react";

export interface FormOption {
  label: string; // Display label for dropdown/radio options
  value: string | number; // Value associated with the option
  children?: FormOption[];
  Id?: number;
  Name?: string;
}

export interface FormField {
  name: string; // Unique name of the field
  type: "text" | "email" | "password" | "number" | "currency" | "textarea" | "select" | "autocomplete" | "multiselect" | "treeSelect" | "treeMultiSelect" | "checkbox" | "radio" | "date" | "file" | "widget" | "hidden" | "masked" | "toggle" | "rating"; // Input type
  label?: string; // Field label displayed in the UI
  tooltip?: string | ReactNode;
  columnSpan?: 1 | 2 | 3 | 4;
  optionText?: string; // Field option for checkbox and radio displayed in the UI
  required?: boolean; // Specifies if the field is mandatory
  mask?: string; // Mask format (e.g., "************" for phone)
  defaultValue?: any | ((initialData?: Record<string, any>) => any);
  options?: FormOption[] | ((formData: Record<string, any>, dependencyValue?: string | number) => Promise<FormOption[]>); // Static or async options
  optionsFromUrl?: string;
  conditionalRender?: (formData: Record<string, any>) => boolean; // Condition to render field
  dependency?: string; // Dependency on another field
  widget?: React.FC<any>; // Custom widget component
  widgetProps?: Record<string, any>; // Props to pass to the custom widget
  validation?: {
    required?: boolean; // Is the field mandatory?
    pattern?: RegExp; // Regex pattern for input validation
    minLength?: number; // Minimum length of the input
    maxLength?: number; // Maximum length of the input
    custom?: (value: any, formData: Record<string, any>) => string | undefined; // Custom validation function
  };
  transform?: (value: any, formData: Record<string, any>) => any; // Function to modify the value before storing/displaying
}

export interface FormGroup {
  title: string; // Group name or label
  columns?: 1 | 2 | 3 | 4; // Layout: single-column or two-column
  fields: FormField[]; // Fields within the group
}

export interface FormAction {
  type: "submit" | "reset" | "custom"; // Supports standard & custom actions
  label: string;
  className: string;
  onClick?: (formData: Record<string, any>) => void; // Custom click handler
}

export interface JFormSchema {
  title?: string | ReactNode;
  formGroups?: FormGroup[];
  fields?: FormField[];
  actions?: {
    showActions?: boolean; // Toggle actions visibility
    buttons?: FormAction[]; // Dynamic list of custom action buttons
    defaultButtons?: boolean; // Show submit & reset buttons by default
  };
  apiEndpoints?: {
    create: string;
    read: string;
    update: string;
    delete: string;
  };
}

export interface TableColumn {
  field: string;
  header: string;
  sortable?: boolean;
}
