import React from "react";
import { ColumnSchema } from "../../../../shared/components/common/data-table/types";
import { MasterPage } from "../../../../shared/components/render-elements";

const permissionSchema: ColumnSchema[] = [
  {
    key: "Id",
    title: "Id",
    hidden: true,
    sorter: false
  },
  {
    key: "BusinessUnitId",
    title: "Business Unit",
    type: "select",
    filterable: true
  },
  {
    key: "RoleId",
    title: "Role",
    type: "select",
    filterable: true
  },
  {
    key: "ModuleId",
    title: "Module",
    type: "select",
    filterable: true
  }
];

const PermissionList: React.FC = () => {
  return <MasterPage
    entityName="Permissions"
    columnsSchema={permissionSchema}
    mode="form"
    apiEndpoints={{
      list: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/permission`
    }}
    routeTemplates={{
      create: "/admin/system/permissions/create",
      edit: "/admin/system/permissions/:id/edit",
    }}
  />;
};

export default PermissionList;
