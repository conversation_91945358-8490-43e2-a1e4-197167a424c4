import React from "react";
import { FormField } from "../types";

interface RadioFieldProps {
  field: FormField;
  value: string | number;
  onChange: (name: string, value: string | number) => void;
}

const RadioField: React.FC<RadioFieldProps> = ({ field, value, onChange }) => {
  return (
    <div>
      <fieldset>
        {/* <legend>
          {field.label} {field.required && <span>*</span>}
        </legend> */}
        {Array.isArray(field.options) && field.options?.map((option) => (
          <div key={option.value}>
            <label>
              <input
                type="radio"
                name={field.name}
                // required={field.required}
                value={option.value}
                checked={value === option.value}
                onChange={() => onChange(field.name, option.value)}
              />
              {option.label}
            </label>
          </div>
        ))}
      </fieldset>
    </div>
  );
};

export default RadioField;
