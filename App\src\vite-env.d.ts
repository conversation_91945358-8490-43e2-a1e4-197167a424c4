/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_Admin_API_BASE_URL: string;
  readonly VITE_APP_API_BASE_URL: string;

  readonly VITE_AUTH_PROVIDER: string;

  // Azure AD Configurations
  readonly VITE_AZURE_AD_CLIENT_ID: string;
  readonly VITE_AZURE_AD_AUTHORITY: string;
  readonly VITE_AZURE_AD_KNOWN_AUTHORITIES: string;
  readonly VITE_AZURE_AD_LOGIN_SCOPES: string;
  readonly VITE_AZURE_AD_REDIRECT_URI: string;
  readonly VITE_AZURE_AD_POST_LOGOUT_REDIRECT_URI: string;

  // Custom JWT Configurations
  readonly VITE_CUSTOM_JWT_LOGIN_URL: string;
  readonly VITE_CUSTOM_JWT_CLIENT_ID: string;
  readonly VITE_CUSTOM_JWT_REDIRECT_URI: string;
  readonly VITE_CUSTOM_JWT_SCOPE: string;

  // Add other variables here if needed
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
