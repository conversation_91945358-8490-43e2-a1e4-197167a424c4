import React from 'react';

interface DragDropProps<T> {
    items: T[];
    onDrop: (newItems: T[]) => void;
    renderItem: (item: T, index: number) => React.ReactNode;
    getKey: (item: T) => string;
    contextId?: string;
}

function DragDrop<T>({ items, onDrop, renderItem, getKey, contextId = 'default' }: DragDropProps<T>) {
    const dragKey = `dragIndex-${contextId}`;

    const handleDragStart = (e: React.DragEvent<HTMLDivElement>, index: number) => {
        e.dataTransfer.setData(dragKey, index.toString());
    };

    const handleDrop = (e: React.DragEvent<HTMLDivElement>, dropIndex: number) => {
        const dragIndexData = e.dataTransfer.getData(dragKey);
        const dragIndex = parseInt(dragIndexData);

        if (
            isNaN(dragIndex) ||
            dragIndex === dropIndex ||
            dragIndex < 0 ||
            dragIndex >= items.length ||
            dropIndex < 0 ||
            dropIndex >= items.length
        ) {
            return;
        }

        const updatedItems = [...items];
        const [draggedItem] = updatedItems.splice(dragIndex, 1);
        updatedItems.splice(dropIndex, 0, draggedItem);
        onDrop(updatedItems);
    };

    const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
    };

    return (
        <div>
            {items.map((item, index) => {
                if (!item) return null;

                let key: string;
                try {
                    key = getKey(item);
                } catch (err) {
                    console.error("Error getting key for item:", item, err);
                    key = `fallback-key-${index}`;
                }

                return (
                    <div
                        key={key}
                        draggable
                        onDragStart={(e) => handleDragStart(e, index)}
                        onDragOver={handleDragOver}
                        onDrop={(e) => handleDrop(e, index)}
                    >
                        {renderItem(item, index)}
                    </div>
                );
            })}
        </div>
    );
}

export default DragDrop;
