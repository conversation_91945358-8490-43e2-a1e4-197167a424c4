export interface OptionItem {
  value: string | number; // Stores the internal value
  label: string; // Displays the human-readable name
}

export interface ActionButton<T> {
  label: string;
  icon?: React.ReactNode;
  onClick: (record: T) => void;
}

export interface ColumnSchema {
  key: string;
  title: string;
  editable?: boolean;
  sorter?: boolean;
  hidden?: boolean;
  type?: "text" | "number" | "date" | "textarea" | "select";
  filterable?: boolean;
  options?: OptionItem[] | (() => Promise<OptionItem[]>);
}
