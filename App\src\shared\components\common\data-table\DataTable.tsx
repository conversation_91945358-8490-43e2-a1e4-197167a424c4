import { ReactNode, useCallback, useMemo, useState } from "react";
import { Table, Modal, message, Tooltip } from "antd";
import { EditOutlined, ReloadOutlined, SaveOutlined, DeleteOutlined, PlusOutlined } from "@ant-design/icons";
import ColumnFilter from "./ColumnFilter";
import EditableCell from "./EditableCell";
import { useInlineEdit } from "./hooks/useInlineEdit";
import { useSorting } from "./hooks/useSorting";
import { useFilter } from "./hooks/useFilter";
import { ActionButton, ColumnSchema } from "./types";
import styles from "./styles.module.css";

interface DataTableProps<T extends { key: string }> {
  header?: ReactNode;
  data: T[];
  columnsSchema: ColumnSchema[];
  inlineEditing?: boolean;
  pageSize?: number;
  allowInlineAdd?: boolean;
  onAdd?: () => void;
  onEdit: (key: string) => void;
  onDelete: (key: string) => Promise<void>;
  onSave?: (key: string, editMode: boolean, updatedData: Partial<T>) => Promise<void>;
  customActions?: ActionButton<T>[];
}

const DataTable = <T extends { key: string }>({
  header,
  data,
  columnsSchema,
  inlineEditing = true,
  allowInlineAdd = true,
  pageSize = 10, // Default page size
  customActions,
  onAdd,
  onEdit,
  onDelete,
  onSave
}: DataTableProps<T>) => {
  const { data: editedData, editingKey, editValues, startEdit, cancelEdit, saveEdit, handleChange, deleteItem } = useInlineEdit(data);
  const { sortedData, handleSort } = useSorting(editedData);
  const { filteredColumns, handleFilterChange, handleResetFilter, applyFilters } = useFilter(editedData);

  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(0);
  const [blankRow, setBlankRow] = useState<T | null>(null);
  const [editMode, setEditMode] = useState<boolean>(false);

  // **Optimized Data Processing Pipeline with Pagination**
  const processedData = useMemo(() => {
    let updatedData = applyFilters(sortedData);
    if (blankRow) updatedData = [blankRow, ...updatedData]; // Add blank row at the top
    return updatedData.slice(currentPage * pageSize, (currentPage + 1) * pageSize);
  }, [sortedData, applyFilters, blankRow, currentPage, pageSize]);

  const handleAdd = () => {
    if (inlineEditing) {
      cancelEdit();
      const newRow = { key: `new-${Date.now()}` } as T;
      setBlankRow({ ...newRow });
      startEdit(newRow.key);
    } else {
      onAdd?.(); // Ensures `onAdd` only runs if provided
    }
  };

  const handleEdit = useCallback((key: string) => {
    setEditMode(true);
    inlineEditing ? startEdit(key) : onEdit(key);
  }, [inlineEditing, startEdit, onEdit]);

  const handleSave = useCallback(async (key: string) => {
    try {
      setLoading(true);
      await onSave?.(key, editMode, editValues);
      saveEdit(key);
      setEditMode(false);
      message.success("Row saved successfully!");
    } catch (error) {
      message.error("Error saving row.");
      console.error(error);
    } finally {
      setLoading(false);
    }
  }, [onSave, editValues, saveEdit]);

  const handleDelete = useCallback((key: string) => {
    Modal.confirm({
      title: "Are you sure you want to delete this row?",
      content: "This action cannot be undone.",
      okText: "Yes, Delete",
      cancelText: "Cancel",
      onOk: async () => {
        try {
          setLoading(true);
          await onDelete?.(key);
          deleteItem(key);
          message.success("Row deleted successfully!");
        } catch (error) {
          message.error("Error deleting row.");
          console.error(error);
        } finally {
          setLoading(false);
        }
      },
    });
  }, [onDelete, deleteItem]);

  const handleAction = useCallback((key: string, action: "edit" | "save" | "delete" | "cancel") => {
    if (action === "edit") handleEdit(key);
    else if (action === "save") handleSave(key);
    else if (action === "delete") handleDelete(key);
    else if (action === "cancel") cancelEdit();
  }, [handleEdit, handleSave, handleDelete]);

  const visibleColumns = columnsSchema.filter((col) => !col.hidden);

  const columns = visibleColumns.map((col: ColumnSchema) => ({
    title: (
      <div className={`flex justify-between items-center gap-2 ${styles.headerRow}`}>
        <span className="w-full cursor-pointer leading-[14px] h-[16px]" onClick={() => handleSort(col.key as keyof T)}>
          {col.title}
          {/* {sortDirection[col.key] === "asc" ? <SortAscendingOutlined /> : <SortDescendingOutlined />} */}
        </span>
        {col.filterable && (
          <ColumnFilter
            columnKey={col.key}
            filterType={col.type || "text"}
            filterOptions={(Array.isArray(col.options) ? col.options : [])}
            isFiltered={filteredColumns.has(col.key)}
            onFilterChange={(columnKey, operator, value) => handleFilterChange(columnKey, operator, value)}
            onResetFilter={handleResetFilter}
          />
        )}
      </div>
    ),
    dataIndex: col.key,
    render: (_: any, record: T) => {
      const isEditing = editingKey === record.key && col.editable && inlineEditing;
      const rawValue = record[col.key as keyof T];

      // Handle inline editing scenario
      if (isEditing) {
        const currentValue = (editValues[col.key as keyof T] ?? rawValue ?? "") as string | number;
        return (
          <EditableCell
            editing={isEditing}
            value={currentValue}
            type={col.type || "text"}
            options={col.options}
            onChange={(newValue) => handleChange(col.key as keyof T, newValue)}
          />
        )
      }

      // If 'options' exist, map value to corresponding label
      if (col.type === "select" && Array.isArray(col.options)) {
        const matchedOption = col.options.find(option => option.value == rawValue);
        return <span>{matchedOption ? matchedOption.label : "Unknown"}</span>;
      }

      const formattedValue = rawValue !== null && rawValue !== undefined ? String(rawValue) : "";

      // Handle date rendering (Ensure ISO format)
      if (col.type === "date" && formattedValue) {
        // TODO: Make configurable based on user locale or settings
        return <span>{new Date(formattedValue).toLocaleDateString("en-US", { year: "numeric", month: "short", day: "numeric" })}</span>;
      }

      // Handle number formatting (Future Enhancement)
      if (col.type === "number") {
        return <span>{Number(formattedValue).toLocaleString()}</span>;
      }

      return <span>{formattedValue}</span>;
    },
  }));

  return (
    <div>
      <div className="flex flex-row gap-5 justify-between mb-2">
        {typeof header === "string" ? <h2 className="font-[700] text-[16px] color-[#0a233e]">{header}</h2> : header}
        {/* Action toolbar */}
        <div className="flex mb-2">
          {allowInlineAdd && (
            <button
              className="flex justify-center w-6 h-6 p-0 border rounded-full cursor-pointer"
              onClick={handleAdd}
            >
              <PlusOutlined />
            </button>
          )}
        </div>
      </div>
      <Table
        rowClassName={styles.evenRow}
        className={styles.dataTable}
        columns={[
          ...columns,
          {
            title: "",
            render: (_, record: T) => (
              <div className="flex gap-2 justify-end">
                {editingKey === record.key && inlineEditing ? (
                  <>
                    <Tooltip title="Save">
                      <button
                        className="w-6 h-6 p-0 border rounded-full cursor-pointer"
                        onClick={() => handleAction(record.key, "save")}
                      >
                        <SaveOutlined />
                      </button>
                    </Tooltip>
                    <Tooltip title="Cancel">
                      <button
                        className="w-6 h-6 p-0 border rounded-full cursor-pointer"
                        onClick={() => handleAction(record.key, "cancel")}
                      >
                        <ReloadOutlined />
                      </button>
                    </Tooltip>
                  </>
                ) : (
                  <>
                    <Tooltip title="Edit">
                      <button
                        className="w-6 h-6 p-0 border rounded-full cursor-pointer"
                        onClick={() => handleAction(record.key, "edit")}
                      >
                        <EditOutlined />
                      </button>
                    </Tooltip>
                    <Tooltip title="Delete">
                      <button
                        className="w-6 h-6 p-0 border rounded-full cursor-pointer"
                        onClick={() => handleAction(record.key, "delete")}
                      >
                        <DeleteOutlined />
                      </button>
                    </Tooltip>
                  </>
                )}

                {/* Render Custom Actions Dynamically */}
                {customActions?.map((action, index) => (
                  <Tooltip title={action.label} key={index} placement="left">
                    <button
                      className="w-6 h-6 p-0 border rounded-full cursor-pointer"
                      onClick={() => action.onClick(record)}
                    >
                      {action.icon}
                    </button>
                  </Tooltip>
                ))}
              </div>
            ),
          },
        ]}
        dataSource={processedData}
        pagination={{
          current: currentPage + 1,
          pageSize,
          showSizeChanger: true,
          pageSizeOptions: ["5", "10", "20", "50"],
          total: sortedData.length,
          onChange: (page) => setCurrentPage(page - 1),
        }}
        loading={loading}
      />
    </div>
  );
};

export default DataTable;
