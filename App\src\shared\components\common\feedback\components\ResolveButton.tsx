import Button from "../../button/Button";
import styles from "./CommentPopup.module.css";

interface ResolveButtonProps {
  statusOpen: boolean;
  toggleStatus: () => void;
}

const ResolveButton: React.FC<ResolveButtonProps> = ({ statusOpen, toggleStatus }) => {
  return (
    <Button className={styles.resolveButton} onClick={toggleStatus}>
      {statusOpen ? "Resolve" : "Reopen"}
    </Button>
  );
};

export default ResolveButton;
