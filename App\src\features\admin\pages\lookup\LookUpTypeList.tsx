import React from "react";
import { MasterPage } from "../../../../shared/components/render-elements";
import { ColumnSchema } from "../../../../shared/components/common/data-table/types";

const lookUpTypeSchema: ColumnSchema[] = [
  {
    key: "Id",
    title: "Id",
    hidden: true,
    editable: false,
    sorter: false
  },
  
  {
    key: "FieldName",
    title: "Name",
    editable: true,
    sorter: true,
    type: "text",
    filterable: true
  },
  {
    key: "Description",
    title: "Description",
    editable: true,
    sorter: true,
    type: "text",
    filterable: true
  },
  
  
];

const LookUpList: React.FC = () => {
  return (
    <MasterPage
      entityName="Lookup Type"
      columnsSchema={lookUpTypeSchema}
      mode="form"
      apiEndpoints={{
        create: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/lookup-types`,
        edit: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/lookup-types`,
        delete: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/lookup-types`,
        list: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/lookup-types`
      }}
      routeTemplates={{
        create: "/admin/system/lookuptypes/create",
        edit: "/admin/system/lookuptypes/:id/edit"
      }}
    />
  );
};

export default LookUpList;
