import apiClient from "../../network/apiClient";

// Define the workflow structure
export interface WorkflowCondition {
  Table?: string;
  Field?: string;
  Operator?: string;
  Value?: string;
  logicOperator?: string;
}

export interface Workflow {
  Id?: number;
  Name: string;
  Description: string;
  Action: string;
  Table: string;
  conditions?: WorkflowCondition[];
}

// Define transition save structure
export interface WorkflowTransitionPayload {
  transition: any;
  rules: any;
  activities: any;
  permissions: any[];
}

export interface Permission {
  key: string;
  Id: string;
  Type: string | string[];
  Name: string;
}

export interface PermissionResponse {
  Record: Permission[];
  UserRights: string;
}

// Define fetch-all response structure
interface FetchResult {
  Record: Workflow[];
  UserRights: string;
}

const BASE_URL = `${import.meta.env.VITE_Admin_API_BASE_URL}/api/workflow`;
const BASE_DEFINITION_URL = `${import.meta.env.VITE_Admin_API_BASE_URL}/api/workflow-definition`;

export const workflowService = {
  // GET - Fetch all workflows
  async fetchAll(): Promise<FetchResult | null> {
    try {
      const response = await apiClient.get<FetchResult>(BASE_URL);
      return response.data ?? null;
    } catch (error) {
      console.error("Error fetching workflows:", error);
      return null;
    }
  },

  // GET - Fetch a single workflow by ID
  async getById(id: number): Promise<Workflow | null> {
    try {
      const response = await apiClient.get<{ Record: Workflow }>(`${BASE_URL}/${id}`);
      return response.data?.Record ?? null;
    } catch (error) {
      console.error(`Error fetching workflow with ID ${id}:`, error);
      return null;
    }
  },

  // POST - Create a new workflow
  async create(workflow: Partial<Workflow>): Promise<Workflow | null> {
    try {
      const response = await apiClient.post<Workflow>(BASE_URL, workflow);
      return response.data ?? null;
    } catch (error) {
      console.error("Error creating workflow:", error);
      return null;
    }
  },

  // PUT - Update an existing workflow
  async update(id: number, updatedWorkflow: Partial<Workflow>): Promise<Workflow | null> {
    try {
      const response = await apiClient.put<Workflow>(`${BASE_URL}/${id}`, updatedWorkflow);
      return response.data ?? null;
    } catch (error) {
      console.error("Error updating workflow:", error);
      return null;
    }
  },

  // DELETE - Soft delete a workflow
  async delete(id: number): Promise<boolean> {
    try {
      await apiClient.delete(`${BASE_URL}/${id}`);
      return true;
    } catch (error) {
      console.error("Error deleting workflow:", error);
      return false;
    }
  },

  // POST - Save a new workflow transition
  async saveTransition(payload: WorkflowTransitionPayload): Promise<boolean> {
    try {
      await apiClient.post(`${BASE_URL}/transition`, payload);
      return true;
    } catch (error) {
      console.error("Error saving workflow transition:", error);
      return false;
    }
  },
  
   // GET - Fetch all permissions
   async fetchAllPermissions(): Promise<PermissionResponse | null> {
    try {
      const response = await apiClient.get<PermissionResponse>(`${BASE_URL}/permissions`);
      return response.data ?? null;
    } catch (error) {
      console.error("Error fetching permissions:", error);
      return {
        Record: [
          {key:'1', Id: "1", Type: "Role", Name: "Admin" },
          {key:'2', Id: "2", Type: "Role", Name: "Editor" },
          {key:'3', Id: "3", Type: "Role", Name: "Viewer" },
          {key:'4', Id: "4", Type: "User", Name: "<EMAIL>" },
          {key:'5', Id: "5", Type: "User", Name: "<EMAIL>" },
          {key:'6', Id: "6", Type: "Group", Name: "Finance Team" },
          {key:'7', Id: "7", Type: "Group", Name: "HR Team" },
        ],
        UserRights: "Admin" // Default user rights for fallback
      };
    }
  },

  // GET - Fetch a single permission by ID
  async getPermissionById(id: string): Promise<Permission | null> {
    try {
      const response = await apiClient.get<{ Record: Permission }>(`${BASE_URL}/permissions/${id}`);
      return response.data?.Record ?? null;
    } catch (error) {
      console.error(`Error fetching permission with ID ${id}:`, error);
      return null;
    }
  },

  // POST - Create a new permission
  async createPermission(permission: Partial<Permission>): Promise<Permission | null> {
    try {
      const response = await apiClient.post<Permission>(`${BASE_URL}/permissions`, permission);
      return response.data ?? null;
    } catch (error) {
      console.error("Error creating permission:", error);
      return null;
    }
  },

  // PUT - Update an existing permission
  async updatePermission(id: string, updatedPermission: Partial<Permission>): Promise<Permission | null> {
    try {
      const response = await apiClient.put<Permission>(`${BASE_URL}/permissions/${id}`, updatedPermission);
      return response.data ?? null;
    } catch (error) {
      console.error("Error updating permission:", error);
      return null;
    }
  },

  // DELETE - Delete a permission
  async deletePermission(id: string): Promise<boolean> {
    try {
      await apiClient.delete(`${BASE_URL}/permissions/${id}`);
      return true;
    } catch (error) {
      console.error("Error deleting permission:", error);
      return false;
    }
  }
};

export const workflowDefinitionService = {
  // GET - Fetch all workflow definitions
  async fetchAll(): Promise<FetchResult | null> {
    try {
      const response = await apiClient.get<FetchResult>(BASE_DEFINITION_URL);
      return response.data ?? null;
    } catch (error) {
      console.error("Error fetching workflows:", error);
      return null;
    }
  },

  // GET - Fetch a single workflow definition by ID
  async getById(id: number): Promise<Workflow | null> {
    try {
      const response = await apiClient.get<{ Record: Workflow }>(`${BASE_DEFINITION_URL}/${id}`);
      return response.data?.Record ?? null;
    } catch (error) {
      console.error(`Error fetching workflow with ID ${id}:`, error);
      return null;
    }
  },

  // POST - Create a new workflow definition
  async create(workflow: Partial<Workflow>): Promise<Workflow | null> {
    try {
      const response = await apiClient.post<Workflow>(BASE_DEFINITION_URL, workflow);
      return response.data ?? null;
    } catch (error) {
      console.error("Error creating workflow:", error);
      return null;
    }
  },

  // PUT - Update an existing workflow definition
  async update(id: number, updatedWorkflow: Partial<Workflow>): Promise<Workflow | null> {
    try {
      const response = await apiClient.put<Workflow>(`${BASE_DEFINITION_URL}/${id}`, updatedWorkflow);
      return response.data ?? null;
    } catch (error) {
      console.error("Error updating workflow:", error);
      return null;
    }
  },

  // DELETE - Soft delete a workflow definition
  async delete(id: number): Promise<boolean> {
    try {
      await apiClient.delete(`${BASE_DEFINITION_URL}/${id}`);
      return true;
    } catch (error) {
      console.error("Error deleting workflow:", error);
      return false;
    }
  },
};
