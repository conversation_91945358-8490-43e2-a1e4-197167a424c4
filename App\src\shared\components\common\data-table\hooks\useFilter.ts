import { useState } from "react";

export const useFilter = <T,>(_initialData: T[]) => {
  const [filters, setFilters] = useState<{ [key: string]: any }>({});
  const [filteredColumns, setFilteredColumns] = useState<Set<string>>(new Set());

  const handleFilterChange = (columnKey: string, operator: string, value: any) => {
    setFilters((prev) => ({ ...prev, [columnKey]: { operator, value } }));
    setFilteredColumns((prev) => new Set(prev).add(columnKey)); // Track filtered column
  };

  const handleResetFilter = (columnKey: string) => {
    setFilters((prev) => {
      const newFilters = { ...prev };
      delete newFilters[columnKey];
      return newFilters;
    });

    setFilteredColumns((prev) => {
      const newSet = new Set(prev);
      newSet.delete(columnKey);
      return newSet;
    });
  };

  const applyFilters = (data: T[]) => {
    const filterEntries = Object.entries(filters); // Process `filters` once

    return data.filter((item) =>
      filterEntries.every(([key, { operator, value }]) => {
        const itemValue = item[key as keyof T];

        if (itemValue === undefined || value === undefined) return false;

        let convertedValue = value;
        if (typeof itemValue === "number") convertedValue = Number(value);
        else if (typeof itemValue === "boolean") convertedValue = value === "true";
        else if (!isNaN(Date.parse(value)) && !isNaN(Date.parse(String(itemValue))))
          convertedValue = new Date(value);

        switch (operator) {
          case "equals":
            return itemValue !== null && itemValue !== undefined && itemValue === convertedValue;
          case "not equals":
            return itemValue !== null && itemValue !== undefined && itemValue !== convertedValue;
          case "<":
            return itemValue !== null && itemValue !== undefined && itemValue < convertedValue;
          case ">":
            return itemValue !== null && itemValue !== undefined && itemValue > convertedValue;
          case "contains":
            return itemValue !== null && itemValue !== undefined && String(itemValue).toLowerCase().includes(String(value).toLowerCase());
          case "starts with":
            return String(itemValue).toLowerCase().startsWith(String(value).toLowerCase());
          case "ends with":
            return String(itemValue).toLowerCase().endsWith(String(value).toLowerCase());
          case "between":
            return itemValue !== null && itemValue !== undefined && itemValue >= value[0] && itemValue <= value[1];
          case "before":
            return new Date(itemValue as string) < new Date(value);
          case "after":
            return new Date(itemValue as string) > new Date(value);
          case "in":
            return Array.isArray(value) && value.includes(itemValue);
          default:
            return true;
        }
      })
    );
  };

  return { filters, filteredColumns, handleFilterChange, handleResetFilter, applyFilters };
};
