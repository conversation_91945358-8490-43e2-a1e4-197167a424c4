import React, { useState } from 'react';
import { Modal, Table } from 'antd';
import { IModelDataElement } from '../types';
import styles from "../styles.module.css";

interface ElementPickerProps {
    dataElements: IModelDataElement[];
    showPicker: boolean;
    setShowPicker: (e: boolean) => void;
    onAdd: (e: any) => void
}

const ElementPicker: React.FC<ElementPickerProps> = ({
    dataElements,
    showPicker,
    setShowPicker,
    onAdd
}) => {

    const [selectedRows, setSelectedRows] = useState<any>([]);

    const columns = [
        {
          title: 'Name',
          dataIndex: 'Name',
          key: 'Name',
        },
        {
          title: 'Slug',
          dataIndex: 'Slug',
          key: 'Slug',
        },
        {
          title: 'Data Type',
          dataIndex: 'DataType',
          key: 'DataType',
        }
    ];

    return <Modal
    closable={true}
    open={showPicker}
    onOk={() => {
        setShowPicker(false);
    }}
    onCancel={() => {
        setShowPicker(false);
    }}
    footer={null}
    centered
    destroyOnClose
    >
         {/* header */}
        <div className='text-[16px] font-bold text-[#0b233f]'>
            { `Add Elements` }
        </div>

        { /* element options */}
        <div className='h-[60vh] overflow-auto p-5'>
            <Table
            dataSource={dataElements}
            className={styles.dataTable}
            rowKey={"Slug"}
            rowSelection={{
                type: "checkbox",
                onChange(_selectedRowKeys, selectedRows) {
                    setSelectedRows(selectedRows);
                },
            }}
            columns={columns}/>
        </div>

        {/* buttons */}
        <div className="flex justify-end space-x-2 mt-[50px]">
            <button onClick={() => {
                onAdd(selectedRows);
                }}
                className="min-w-[100px] border border-[#0b233f] bg-[#0b233f] h-[30px] text-[14px] font-medium text-white cursor-pointer mr-[10px]">
                Add
            </button>
        </div>
    </Modal>;
}

export default ElementPicker;
