import { AuthenticationResult, BrowserCacheLocation, PublicClientApplication } from "@azure/msal-browser";
import { AuthProvider } from "./AuthProvider";
import { setAuthToken } from "../../api/utils/axiosInstance";
import { AuthEventType, AuthUser } from "./authContext.types";

const isIE = window.navigator.userAgent.indexOf("MSIE ") > -1 || window.navigator.userAgent.indexOf("Trident/") > -1;

const msalInstance = new PublicClientApplication({
  auth: {
    clientId: import.meta.env.VITE_AZURE_AD_CLIENT_ID || "",
    authority: import.meta.env.VITE_AZURE_AD_AUTHORITY || "",
    redirectUri: import.meta.env.VITE_AZURE_AD_REDIRECT_URI || "",
    postLogoutRedirectUri: import.meta.env.VITE_AZURE_AD_POST_LOGOUT_REDIRECT_URI,
    navigateToLoginRequestUrl: true
  },
  cache: {
    cacheLocation: BrowserCacheLocation.LocalStorage, // Configures cache location. "sessionStorage" is more secure, but "localStorage" gives you SSO between tabs.
    storeAuthStateInCookie: isIE, // Set this to "true" if you are having issues on IE11 or Edge. Remove this line to use Angular Universal
  },
});

export class AzureAuthProvider implements AuthProvider {
  async login(callback?: (eventType: string, data?: any) => void): Promise<void> {
    try {
      const response: AuthenticationResult = await msalInstance.loginPopup();

      msalInstance.setActiveAccount(response.account);
      // Set token in Axios instance
      setAuthToken(response.accessToken);

      if (callback) callback(AuthEventType.LOGIN_SUCCESS, response.account);
    } catch (error) {
      console.error("Azure login failed:", error);
      if (callback) callback(AuthEventType.LOGIN_FAILURE, error);
    }
  }

  async logout(callback?: (eventType: string) => void): Promise<void> {
    try {
      await msalInstance.logoutPopup();

      // Clear the token from Axios
      setAuthToken(null);

      if (callback) callback(AuthEventType.LOGOUT_SUCCESS);
    } catch (error) {
      console.error("Azure logout failed:", error);
      if (callback) callback(AuthEventType.LOGOUT_FAILURE);
    }
  }

  async isAuthenticated() {
    const accounts = msalInstance.getAllAccounts();
    return accounts.length > 0 ? true : false;
  }

  async getUser(): Promise<AuthUser | null> {
    const accounts = msalInstance.getAllAccounts();
    const token: AuthUser = { access_token: "", id_token: "" };
    return accounts.length > 0 ? token : null;
  }

  async getAccessToken(): Promise<string | null> {
    try {
      const activeAccount = msalInstance.getActiveAccount();
      if (!activeAccount) throw new Error("No active account!");

      const response = await msalInstance.acquireTokenSilent({
        scopes: ["User.Read"], // Replace with your required scopes
        account: activeAccount,
      });

      return response.accessToken;
    } catch (error) {
      console.error("Failed to acquire token silently:", error);
      return null;
    }
  }
}
