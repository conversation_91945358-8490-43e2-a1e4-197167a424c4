import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { JForm, JFormSchema } from "../../../../../shared/components/common/json-form";
import { message } from "antd";
import { Loader } from "../../../../../shared/components/common";
import { Country, countryService } from "../../../../../api/services/admin/countryService";
import CountryDetailList from "./CountryDetailList";

const CountryForm: React.FC<{ mode: "create" | "edit" }> = ({ mode }) => {
  const navigate = useNavigate();
  const { id } = useParams(); // Get country ID from URL params for edit mode
  const [formData, setFormData] = useState<Country | null>(null);
  const countryId = Number(id);

  const schema: JFormSchema = {
    title: `${mode} Country`,
    formGroups: [
      {
        title: "",
        columns: 3,
        fields: [
          {
            name: "Id",
            type: "hidden",
            required: true,
            defaultValue: 0
          },
          {
            name: "Code",
            type: "text",
            label: "Code",
            required: true,
            validation: {
              required: true,
              maxLength: 5
            }
          },
          {
            name: "Description",
            type: "text",
            label: "Description",
            required: true,
            validation: {
              required: true,
              maxLength: 500
            }
          },
          {
            name: "Active",
            type: "toggle",
            label: "Active",
            required: true,
            transform: (value) => (value ? 1 : 0),
            defaultValue: 1,
            validation: {
              required: true,
              custom: (value) => {
                const num = Number(value)
                return num === 0 || num === 1 ? undefined : "Value must be 0 or 1";
              },
            }
          },
        ]
      }
    ]
  };

  useEffect(() => {
    if (mode === "edit" && countryId) {
      countryService.getById(countryId)
        .then(setFormData)
        .catch((error) => console.error("Error fetching country data:", error));
    }
  }, [mode, countryId]);
   
  const handleSubmit = async (formValues: Record<string, any>) => {
    try {
      const response = mode === "edit" && countryId
        ? await countryService.update(countryId, formValues)
        : await countryService.create(formValues);
  
      if (response) {
        message.success(`Country ${mode === "edit" ? "updated" : "created"} successfully.`);
        navigate("..");
      } else {
        message.error(`Failed to ${mode === "edit" ? "update" : "create"} country. Please try again.`);
      }
    } catch (error: any) {
      console.error("Error submitting form:", error);
      message.error(error?.response?.data?.message || "An unexpected error occurred.");
    }
  };

  return (
    <>
      <div className="flex flex-col gap-2 bg-white p-[30px] rounded-[6px]">
        {mode === "edit" && !formData ? (
          <Loader />
        ) : (
          <JForm schema={schema} onSubmit={handleSubmit} data={formData || {}} />
        )}
      </div>
      <div className="mt-[15px]">
        {id && <CountryDetailList countryId={id} />}
      </div>
    </>
  );
};

export default CountryForm;
