import React, { useState } from 'react';
import { Modal } from 'antd';
import FormRenderer from '../../form-renderer/FormRenderer';

import styles from "../styles.module.css";

interface FormPreviewProps {
    schema: Record<any,any>;
    showPreview: boolean;
    setShowPreview: (e: boolean) => void
}

const FormPreview: React.FC<FormPreviewProps> = ({
    schema,
    showPreview,
    setShowPreview
}) => {

    // Temporary State Handling For the Purpose of Error Fixing 
    const [datum, setDatum] = useState<Record<string, any>>({});
    const handleSave = () => {
        console.log("Preview save:", datum);      
    };
    
    return <Modal
    closable={true}
    open={showPreview}
    width={'95%'}
    height={'90vh'}
    onOk={() => {
        setShowPreview(false);
    }}
    onCancel={() => {
        setShowPreview(false);
    }}
    footer={null}
    className={styles.formPreviewContainer}
    centered
    destroyOnClose
    >
        <div className={`w-[100%] p-[20px] ${styles.formPreviewContainer}`}>
            {/* header */}
            <div className='text-[16px] font-bold text-[#0b233f]'>
                { `Form Preview` }
            </div>

            { /* form renderer */}
            <div className='w-[100%] min-h-[65vh]'>
            <FormRenderer datum={datum} setDatum={setDatum} onSave={handleSave} schema={schema} />
            </div>
        </div>
    </Modal>;
}

export default FormPreview;