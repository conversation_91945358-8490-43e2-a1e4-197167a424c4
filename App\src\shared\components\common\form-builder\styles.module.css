.builderHeader {
    font: #4d4d4d 20px;
    font-weight: 600;
    height: 60px;
    background-color: #fff;
    border-radius: 6px;
    margin-top: 10px;
}

.addSectionBtn {
    border-radius: 100%;
    margin-left: 10px;
    width: 24px;
    display: flex;
    height: 24px;
    border: 1px solid #00aeef;
    align-items: center;
    justify-content: center;
  }
  
  .addSectionBtn:hover {
    border-radius: 100%;
    width: 24px;
    display: flex;
    height: 24px;
    border: 1px solid #0b3456;
    align-items: center;
    justify-content: center;
    background-color: #0b3456;
    color: #fff;
  }

  .sectionContainer {
    border-bottom: 1px solid #CCCCCC !important;
    margin-left: 0px;
    margin-right: 5px;
    border-radius: 0 !important;
  }

  .sectionHeader {
    position: relative;
    display: flex;
    flex-wrap: nowrap;
    color: rgba(0, 0, 0, 0.88);
    line-height: 1.5714285714285714;
    cursor: pointer;
    transition: all 0.3s, visibility 0s;
    margin-top: 10px;
    margin-bottom: 10px;
    margin-left: 5px;
  }

  .sectionHeader .sectionTitle {
    font-weight: 700;
    color: #4d4d4d;
    text-transform: capitalize;
    width: calc(100% - 60px);
  }

  .subSectionContainer {
    border: 0px;
    margin-left: 5px;
    margin-bottom: 10px;
    border-left: 3px solid #0b233f;
  }

  .selectedSubSectionItem, .subSectionItem {
    height: 35px;
    margin-left: 20px !important;
    display: flex;
    margin-left: 5px;
    margin-top: 5px;
  }

  .subSectionsrNo {
    height: 20px;
    width: 20px;
    background-color: #999;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: 11px
  }

  .selectedSubSectionItem .subSectionsrNo{
    background-color: #00aeef;
    color: #fff;
  }

  .subSectionText {
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
    margin-left: 5px;
  }

  .selectedSubSectionItem .subSectionText {
    color: #00aeef;
  }

  .subSectionAddIcon {
    border-radius: 100%;
    width: 24px;
    display: block;
    height: 24px;
    border: 1px solid #00aeef !important;
    padding-left: 5px;
    padding-top: 1px;
    color: #0b233f !important;
  }

  .subSectionAddIcon svg {
    color: #0b233f;
  }

  .subSectionAddIcon .anticon-plus {
    border: 1px solid #00aeef !important;
    color: #0b233f !important;
  }

  .subSectionAddText {
    color: #4d4d4d;
    font-size: 14px;
    font-weight: 500;
    margin-left: 5px;
    margin-top: 4px;
 }

 .elementSection1 {
    display: flex;
    justify-content: flex-start!important;
    align-items: center;
    padding-left: 30px;
    font-size: 18px;
    font-weight: bold;
    white-space: nowrap;
    text-transform: uppercase;
 }

 .elementSection2 {
    display: flex;
    justify-content: flex-start!important;
    align-items: center;
    font-size: 18px;
    white-space: nowrap;
    padding-left:0px;
 }

 .elementSectionGap {
    border: 1px solid #d2d2d2;
    height: 30px;
    margin-right: 25px;
    margin-left: 20px;
    cursor: default;
    float: right;
    align-items: center;
    justify-content: center;
    display: flex;
    margin-top: 15px;
 }

 .elementContainer  {
    height: max-content;
    width: calc(100% - 40px);
    border-radius: 0 0 8px 8px;
    background-color: white;
    margin-top: 5px;
    border: 1px solid #d9d9d9;
 }

 .elementHeaderActiveRow {
    background-color: #0b3456;
    color: #fff;
    position: relative;
    display: flex;
    flex-wrap: nowrap;
    padding: 12px 16px;
    line-height: 1.5714285714285714;
    cursor: pointer;
    border-radius: 6px !important;
    height: 60px;
    padding-left: 20px !important;
 }

 .elementHeaderRow {
    background-color: #fff;
    color: #0B3456;
    position: relative;
    display: flex;
    flex-wrap: nowrap;
    padding: 12px 16px;
    line-height: 1.5714285714285714;
    cursor: pointer;
    border-radius: 6px !important;
    height: 60px;
    padding-left: 20px !important;
 }

 .elementHeaderRow .elementIndex {
    background-color: #00aeef;
    color: white;
 }

 .elementIndex {
    background-color: #fff;
    color: #0b233f;
    height: 28px;
    width: 28px;
    text-align: center;
    align-items: center;
    border-radius: 6px;
    margin-left: 10px;
    margin-right: 15px;
 }

 .elementHeaderRow .elementTitle {
    color: #0b3456 !important;
 }

 .elementTitle {
    color: #fff !important;
    font-weight: 700;
    align-items: center;
    display: block;
    font-size: 18px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    width: 100%;
    max-width: calc(100% - 60px);
 }

 .elementContentRow {
    overflow-x: hidden;
    overflow-y: scroll;
    height: 250px;
    border: none;
    margin-right: 10px;
    margin-top: 20px;
    margin-bottom: 20px;
 }

 .dataTable {
    background-color: #fff;
    border: 1px solid #dbdbdb;
    border-radius: 6px;
    padding: 10px;
  }
  
  .dataTable th {
    background-color: #fff !important;
    padding: 4px !important;
    /* height: 32px; */
  }
  
  .headerRow {
    color: #000000e0;
    font-weight: 600;
  }
  
  .dataTable tr td {
    border: none !important;
    padding: 4px !important;
  }
  
  .dataTable tr td input {
    padding: 2px 4px !important;
    background-color: #fff;
  }
  
  .dataTable tr:hover {
    background-color: #e1ebf0 !important;
  }
  
  .dataTable tr:nth-child(odd) {
    background-color: #f7f7f7;
  }

  .btnDefault {
    align-items: center;
    justify-content: center;
    margin: 0 0px 0 5px;
    height: 30px;
    border: 1px solid #00aeef !important;
    color: #0b233f !important;
    height: 30px;
    font-size: 14px !important;
    font-weight: 500 !important;
    display: flex;
    text-transform: uppercase;
  }
  
  .btnDefault:hover {
    align-items: center;
    justify-content: center;
    margin: 0 0px 0 5px;
    border: 1px solid #0b233f !important;
    background-color: #0b233f !important;
    height: 30px;
    font-size: 14px;
    font-weight: 500;
    color: #fff !important;
    cursor: pointer;
    display: flex;
    text-transform: uppercase;
  }

  .btnDefault:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    border-color: #a0a0a0 !important;
    color: #6c6c6c !important;
    background-color: #f0f0f0 !important;
  }

  .formPreviewContainer {
    background: linear-gradient(to bottom, #d5e7f1, #f2f2f2);
  }
  