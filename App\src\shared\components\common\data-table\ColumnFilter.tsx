import React, { useState, useRef, useEffect } from "react";
import { CheckOutlined, FilterFilled, FilterOutlined, ReloadOutlined, } from "@ant-design/icons";
import { getOperatorsByType } from "./utils/detectOperators";
import { OptionItem } from "./types";

interface ColumnFilterProps {
  columnKey: string;
  filterType: "text" | "number" | "date" | "textarea" | "select";
  filterOptions?: OptionItem[];
  isFiltered: boolean; // Prop to track applied filters
  onFilterChange: (columnKey: string, operator: string, value: any) => void;
  onResetFilter: (columnKey: string) => void;
}

const ColumnFilter: React.FC<ColumnFilterProps> = ({ columnKey, filterType, filterOptions, isFiltered, onFilterChange, onResetFilter }) => {
  const [isPanelOpen, setIsPanelOpen] = useState(false);
  const [filterPosition, setFilterPosition] = useState({});
  const [selectedOperator, setSelectedOperator] = useState<string>("");
  const [filterValue, setFilterValue] = useState<any>(null);
  const filterRef = useRef<HTMLDivElement>(null);

  const operators = getOperatorsByType(filterType);

  const handleFilterClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    const buttonRect = event.currentTarget.getBoundingClientRect(); // Get button position
    const screenWidth = window.innerWidth; // Get current screen width

    // Calculate position dynamically
    const shouldOpenRight = buttonRect.left < screenWidth / 2;

    setFilterPosition(shouldOpenRight ? { left: 0 } : { right: 0 });
    setIsPanelOpen(!isPanelOpen);
  };

  const handleOperatorChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedOperator(event.target.value);
    setFilterValue(null);
  };

  const handleValueChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFilterValue(event.target.value);
  };

  const handleRangeChange = (event: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const newRange = [...(filterValue || [0, 0])];
    newRange[index] = event.target.value;
    setFilterValue(newRange);
  };

  const applyFilter = () => {
    onFilterChange(columnKey, selectedOperator, filterValue);
    setIsPanelOpen(false);
  };

  const resetFilter = () => {
    setSelectedOperator(operators.length > 0 ? operators[0] : ""); // Select first available operator dynamically
    setFilterValue(null);
    onResetFilter(columnKey); // Reset filter state in parent component
  };

  const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedValue = e.target.value;

    // Check the type of the first option to decide conversion
    const isNumber = filterOptions && filterOptions?.length > 0 && typeof filterOptions[0].value === "number";

    setFilterValue(isNumber ? Number(selectedValue) : selectedValue); // Convert only if needed
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (filterRef.current && !filterRef.current.contains(event.target as Node)) {
        setIsPanelOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Ensures filterValue is set automatically if there's only one option
  useEffect(() => {
    if (!selectedOperator && operators.length > 0) {
      setSelectedOperator(operators[0]); // Set first available operator dynamically
    }
    if (!filterValue && filterOptions && filterOptions?.length > 0) {
      setFilterValue(filterOptions[0].value); // Set first available option dynamically
    }
  }, [operators, filterOptions, filterOptions, filterValue]);

  return (
    <div className="relative inline-block">
      <button onClick={handleFilterClick} className="text-blue-500 hover:text-blue-700 cursor-pointer">
        {isFiltered ? <FilterFilled /> : <FilterOutlined />}
      </button>

      {isPanelOpen && (
        <div
          ref={filterRef}
          style={{ position: "absolute", ...filterPosition }}
          className={`flex flex-col z-10 mt-2 bg-white border border-gray-300 shadow-lg p-4 rounded-sm w-64 transition-all duration-300 ease-in-out`}>
          <label className="block text-gray-700">Operator:</label>
          <select value={selectedOperator} onChange={handleOperatorChange} className="w-full border border-gray-300 rounded p-2">
            {operators.map((op) => (
              <option key={op} value={op}>{op}</option>
            ))}
          </select>

          {filterType === "text" && (
            <div className="mt-2">
              <label className="block text-gray-700">Value:</label>
              <input type="text" placeholder="Enter text..." onChange={handleValueChange} className="w-full border border-gray-300 rounded p-2" />
            </div>
          )}

          {filterType === "number" &&
            (selectedOperator === "between" ? (
              <div className="mt-2 flex gap-2">
                <input type="number" placeholder="Min" onChange={(e) => handleRangeChange(e, 0)} className="w-1/2 border border-gray-300 rounded p-2" />
                <input type="number" placeholder="Max" onChange={(e) => handleRangeChange(e, 1)} className="w-1/2 border border-gray-300 rounded p-2" />
              </div>
            ) : (
              <div className="mt-2">
                <label className="block text-gray-700">Value:</label>
                <input type="number" placeholder="Enter number..." onChange={handleValueChange} className="w-full border border-gray-300 rounded p-2" />
              </div>
            ))}

          {filterType === "date" &&
            (selectedOperator === "between" ? (
              <>
                <div className="mt-2 flex flex-col gap-2">
                  <label className="block text-gray-700">From Date:</label>
                  <input type="date" onChange={(e) => handleRangeChange(e, 0)} className="w-full border border-gray-300 rounded p-2" />
                </div>
                <div className="mt-2 flex flex-col gap-2">
                  <label className="block text-gray-700">To Date:</label>
                  <input type="date" onChange={(e) => handleRangeChange(e, 1)} className="w-full2 border border-gray-300 rounded p-2" />
                </div>
              </>
            ) : (
              <div className="mt-2">
                <label className="block text-gray-700">Date:</label>
                <input type="date" onChange={handleValueChange} className="w-full border border-gray-300 rounded p-2" />
              </div>
            ))}

          {filterType === "select" && (
            <div className="mt-2">
              <label className="block text-gray-700">Select Value:</label>
              <select
                className="w-full border border-gray-300 rounded p-2"
                value={filterValue}
                onChange={handleSelectChange}>
                {/* Default placeholder option */}
                <option value="" disabled>
                  {`-Select-`}
                </option>

                {filterOptions && filterOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          )}

          <div className="mt-4 flex gap-5">
            <button
              className="text-gray-500 h-6 w-6 p-0 rounded-full border hover:bg-gray-700 hover:text-white cursor-pointer"
              onClick={applyFilter}>
              <CheckOutlined />
            </button>
            <button
              className="text-gray-500 h-6 w-6 p-0 rounded-full border hover:bg-gray-700 hover:text-white cursor-pointer"
              onClick={resetFilter}>
              <ReloadOutlined />
            </button>
            {/* <button onClick={() => setIsPanelOpen(false)} className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-700">
              Close
            </button> */}
          </div>
        </div>
      )}
    </div>
  );
};

export default ColumnFilter;
