import React from 'react';

import styles from "../styles.module.css";

interface LeftNavigationProps {
    subSections: any[];
    selectedSubSection: string;
    setSelectedSubSection: (key: string) => void;
}

const LeftNavigation: React.FC<LeftNavigationProps> = ({
    subSections,
    selectedSubSection,
    setSelectedSubSection
}) => {
    return <ul className={ `rounded-[6px] bg-white h-[71.5vh] ${styles.leftNavigationContainer}`}>
        {
            subSections.map((subSection, i) => {
                return <li key={`subsection_${i}`} 
                onClick={() => {setSelectedSubSection(subSection.key)}} 
                className={`${styles.subSectionItem} ${subSection.key===selectedSubSection ? styles.selectedSubSectionItem : ''}`}>
                    <span className={styles.subSectionContainer}>
                        <span className={styles.subSectionIndex} >{i + 1}</span>
                        <span>{subSection.title}</span>
                    </span>
                </li>
            })
        }
    </ul>;
}

export default LeftNavigation;