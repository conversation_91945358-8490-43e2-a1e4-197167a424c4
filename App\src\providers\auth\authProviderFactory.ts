import { AzureAuthProvider } from "./AzureAuthProvider";
import { CustomAuthProvider } from "./CustomAuthProvider";
import { AuthProvider } from "./AuthProvider";

const authProviderFactory = (): AuthProvider => {
  const provider = import.meta.env.VITE_AUTH_PROVIDER;

  if (!provider) {
    console.warn("No authentication provider set.");
  }

  switch (provider) {
    case "AzureAD":
      return new AzureAuthProvider();
    case "CustomJWT":
      return new CustomAuthProvider();
    default:
      throw new Error("Invalid authentication provider");
  }
};

export const authProvider = authProviderFactory();
