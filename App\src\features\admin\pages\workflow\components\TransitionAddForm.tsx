
import React, { useEffect, useRef, useState } from "react";
import ExpandableSection from "./ExpandableSection";
import PermissionTable from "./PermissionTable";
import { Transition } from "../types/types"; // Adjust the import path as necessary

const WorkflowTransitionForm: React.FC = () => {
  const dataRef = useRef<Transition>({
    from: "",
  to: "",
  rules: {
    ruleConditions: "",
    ruleValidations: "",
    ruleDataCollection: "",
  },
  activities: {
    notificationType: "",
    Email: "",
  },
  permissions: [],
  });

  const [from, setFrom] = useState("");
  const [to, setTo] = useState("");
  const [notificationType, setNotificationType] = useState("");
  const [email, setEmail] = useState("");
  const [ruleConditions, setRuleConditions] = useState("");
  const [ruleValidations, setRuleValidations] = useState("");
  const [ruleDataCollection, setRuleDataCollection] = useState("");

  useEffect(() => {
    dataRef.current.from = from;
    dataRef.current.to = to;
}, [from, to]);

  useEffect(() => {
    dataRef.current.rules = {
      ruleConditions,
      ruleValidations,
      ruleDataCollection,
    };
  }, [ruleConditions, ruleValidations, ruleDataCollection]);

  useEffect(() => {
    dataRef.current.activities = {
      notificationType,
      Email: email,
    };
  }, [notificationType, email]);

  useEffect(() => {
    (window as any).saveWorkflowTransition = () => {
      const payload = { ...dataRef.current };
      (window as any).setWorkflowTransition?.(payload);
    };
  }, []);

  // Helper to count filled rules
  const filledRulesCount = [ruleConditions, ruleValidations, ruleDataCollection].filter(Boolean).length;
  const filledActivitiesCount = [notificationType, email].filter(Boolean).length;

  return (
    <div className="">
      <h1 className="text-[18px] font-bold normal-case tracking-normal whitespace-pre text-[#46464C] mb-4">New Workflow Transition</h1>

      {/* Transition from/to */}
      <section className="grid grid-cols-1 sm:grid-cols-4 gap-6">
        <div>
          <label htmlFor="from" className="block mb-2 font-medium text-gray-700">
          <span className="text-red-500 mr-1">*</span>
          From</label>
          <select
            id="from"
            value={from}
            onChange={(e) => setFrom(e.target.value)}
            required
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
          />
        </div>
        <div>
          <label htmlFor="to" className="block mb-2 font-medium text-gray-700">
          <span className="text-red-500 mr-1">*</span>
          To</label>
          <select
            id="to"
            value={to}
            onChange={(e) => setTo(e.target.value)}
            required
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
          />
        </div>
      </section>
      <div className="border-t border-gray-200 my-5"></div>

      {/* Rules section */}
      <ExpandableSection title="Rules" count={filledRulesCount} >
        <div className="space-y-6">
          <ExpandableSection title="Conditions" count={ruleConditions ? 1 : 0} >
            <select
              value={ruleConditions}
              onChange={(e) => setRuleConditions(e.target.value)}
              className="w-[200px] border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            >
              <option value="">Select Conditions</option>
              <option value="condition1">Condition 1</option>
              <option value="condition2">Condition 2</option>
              <option value="condition3">Condition 3</option>
            </select>
          </ExpandableSection>

          <ExpandableSection title="Validations" count={ruleValidations ? 1 : 0}>
            <select
              value={ruleValidations}
              onChange={(e) => setRuleValidations(e.target.value)}
              className="w[200px] border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            >
              <option value="">Select Validations</option>
              <option value="validation1">Validation 1</option>
              <option value="validation2">Validation 2</option>
              <option value="validation3">Validation 3</option>
            </select>
          </ExpandableSection>

          <ExpandableSection title="Data Collection" count={ruleDataCollection ? 1 : 0}>
            <select
              value={ruleDataCollection}
              onChange={(e) => setRuleDataCollection(e.target.value)}
              className="w-[200px] border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            >
              <option value="">Select Data Collection</option>
              <option value="data1">Data Collection 1</option>
              <option value="data2">Data Collection 2</option>
              <option value="data3">Data Collection 3</option>
            </select>
          </ExpandableSection>
        </div>
      </ExpandableSection>

      {/* Activities section */}
      <ExpandableSection title="Activities" count={filledActivitiesCount}>
        <div className="space-y-6 ">
          <div>
            <label htmlFor="notificationType" className="block mb-2 font-medium text-gray-700">Notification</label>
            <select
              id="notificationType"
              value={notificationType}
              onChange={(e) => setNotificationType(e.target.value)}
              required
              className="w-[200px] border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            >
              <option value="">Notification</option>
              <option value="notify1">Notify 1</option>
              <option value="notify2">Notify 2</option>
              <option value="notify3">Notify 3</option>
            </select>
          </div>
          <div>
            <label htmlFor="email" className="block mb-2 font-medium text-gray-700">Email</label>
            <select
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="w-[200px] border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            >
              <option value="">Email</option>
              <option value="<EMAIL>"><EMAIL></option>
              <option value="<EMAIL>"><EMAIL></option>
            </select>
          </div>
        </div>
      </ExpandableSection>

      {/* Permissions */}
      <ExpandableSection title="Permissions" count={dataRef.current.permissions.length}>
        <div className="bg-white rounded-lg shadow p-6">
          <PermissionTable />
        </div>
      </ExpandableSection>
    </div>
  );
};

export default WorkflowTransitionForm;
