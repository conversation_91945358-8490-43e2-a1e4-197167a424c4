import apiClient from "../../network/apiClient";

// Define user structure
export interface FiscalYear {
  Id: number;
  FiscalYearCode: string;
  StartDate: string;
  EndDate: string;
  Active: number;
  CreatedById?: number | null;
  CreatedAt?: string | null;
  UpdatedById?: number | null;
  UpdatedAt?: string | null;
  IsDeleted: boolean;
}

// Define fetch-all response structure
interface FetchResult {
  Record: FiscalYear[];
  UserRights: string;
}

const BASE_URL = `${import.meta.env.VITE_PGM_API_BASE_URL}/api/fiscal-year`;

export const fiscalYearService = {
  // GET - Fetch all fiscalYear
  async fetchAll(): Promise<FetchResult | null> {
    try {
      const response = await apiClient.get<FetchResult>(BASE_URL);
      return response.data ?? null;
    } catch (error) {
      console.error("Error fetching fiscal years:", error);
      return null;
    }
  },

  /**
 * GET - Fetch a single fiscalyear by its ID.
 * @param fiscalYearId - The ID of the fiscalyear to fetch.
 */
  async getById(fiscalYearId: number): Promise<FiscalYear> {
    try {
      const response = await apiClient.get<{ Record: FiscalYear }>(`${BASE_URL}/${fiscalYearId}`);
      return response.data.Record ?? null;
    } catch (error) {
      console.error(`Error fetching fiscal year with ID ${fiscalYearId}:`, error);
      throw error;
    }
  },

  // POST - Create a new fiscalyear
  async create(fiscalyear: Partial<FiscalYear>): Promise<FiscalYear | null> {
    try {
      const response = await apiClient.post<FiscalYear>(BASE_URL, fiscalyear);
      return response.data ?? null;
    } catch (error) {
      console.error("Error creating fiscal year:", error);
      return null;
    }
  },

  // PUT - Update an existing fiscalyear
  async update(fiscalYearId: number, updatedFiscalYear: Partial<FiscalYear>): Promise<FiscalYear | null> {
    try {
      const response = await apiClient.put<FiscalYear>(`${BASE_URL}/${fiscalYearId}`, updatedFiscalYear);
      return response.data ?? null;
    } catch (error) {
      console.error("Error updating fiscal year:", error);
      return null;
    }
  },

  // DELETE - Remove a fiscalyear
  async delete(fiscalYearId: number): Promise<boolean> {
    try {
      await apiClient.delete(`${BASE_URL}/${fiscalYearId}`);
      return true;
    } catch (error) {
      console.error("Error deleting fiscal year:", error);
      return false;
    }
  }
};
