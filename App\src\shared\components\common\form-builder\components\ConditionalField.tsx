import React, { useState } from "react";
import { Select, Input } from "antd";

interface ConditionalFieldProps {
    dropdownOptions: string[][];
    placeholders?: string[];
}

const ConditionalField: React.FC<ConditionalFieldProps> = ({
    dropdownOptions,
    placeholders,
}) => {
    const [isChecked, setIsChecked] = useState(false);

    return (
        <div>
            <style>
                {`
                .custom-checkbox:checked::after {
                content: '';
                position: absolute;
                top: 40%;
                left: 50%;
                margin-bottom: 3px;
                width: 5px;
                height: 10px;
                border: solid white;
                border-width: 0 2px 2px 0;
                transform: translate(-50%, -50%) rotate(45deg);
                }

                .custom-checkbox {
                position: relative;
                }
                `}
            </style>

            <div className="flex items-center gap-2 text-[14px] ml-[30px]">
                <input
                    type="checkbox"
                    checked={isChecked}
                    className="custom-checkbox w-[16px] h-[16px] appearance-none checked:bg-[#00AEEF] rounded-sm"
                    onChange={(e) => setIsChecked(e.target.checked)}
                />
                <span>Conditional</span>
            </div>

            {isChecked && (
                <>
                    <div className="font-bold ml-[30px] mt-[25px] mb-[35px] text-[14px]">
                        Show only if:
                    </div>
                    <div className="flex gap-4 mb-[30px] ml-[45px] mr-[40px] w-full">
                        <Select
                            showSearch
                            placeholder={placeholders?.[0] || "Select"}
                            style={{ width: "40%" }}
                            options={dropdownOptions[0].map((option) => ({
                                label: option,
                                value: option,
                            }))}
                            className="text-[14px]"
                        />
                        <Select
                            showSearch
                            placeholder={placeholders?.[1] || "Select"}
                            style={{ width: "20%" }}
                            options={dropdownOptions[1].map((option) => ({
                                label: option,
                                value: option,
                            }))}
                            className="text-[14px]"
                        />
                        <Input
                            placeholder={placeholders?.[2] || "Enter value"}
                            className="text-[14px]"
                            style={{ width: "30%" }}
                        />
                    </div>
                </>
            )}
        </div>
    );
};

export default ConditionalField;
