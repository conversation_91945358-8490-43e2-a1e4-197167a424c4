export interface IFormSection {
    key: string;
    title: Record<string, string>;
    children: IFormSection[];
}

export interface IFormElement {
    elementId: number;
    elementPath: string;
    title: Record<string, string>;
    description: Record<string, string>;
    slug: string;
    dataType: string;
    widget?: string;
    options?: { value: string; label: string }[];
    optionsFromURL?: string;
}

export interface IModelForm {
    title: Record<string, string>,
    sections: IFormSection[],
    elements: IFormElement[]
}

export interface IModelDataElement {
    Title: string;
    Slug: string;
    DataType: string;
    Format: string;
    MaxLength: number;
    Options: any;
}
