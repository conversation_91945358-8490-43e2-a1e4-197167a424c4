import React from "react";
import { FormField } from "../types";
import DateUtils from "../utils/dateUtils";

interface DateFieldProps {
  field: FormField;
  value: string;
  onChange: (name: string, value: string) => void;
}

const DateField: React.FC<DateFieldProps> = ({ field, value, onChange }) => {
  return (
    <div>
      <input
        type="date"
        id={field.name}
        name={field.name}
        // required={field.required}
        value={DateUtils.formatDate(value, "YYYY-MM-DD")}
        onChange={(e) => onChange(field.name, e.target.value)}
      />
    </div>
  );
};

export default DateField;
