import apiClient from "../../network/apiClient";

// Define user structure
export interface Framework {
  Id: number;
  Name: string;
  FrameworkTypeId: number;
  CreatedById?: number | null;
  CreatedAt?: string | null;
  UpdatedById?: number | null;
  UpdatedAt?: string | null;
  IsDeleted: boolean;
}

interface FrameworkTypeId {
  Value: number;
  DisplayText: string;
  DisplayCode?: string | null;
  Active: number;
  GroupId?: number | null;
  GroupName?: string | null;
}

// Define fetch-all response structure
interface FetchResult {
  Record: Framework[];
  FrameworkTypeId: FrameworkTypeId[];
  UserRights: string;
}

const BASE_URL = `${import.meta.env.VITE_PGM_API_BASE_URL}/api/framework`;

export const frameworkService = {
  // GET - Fetch all framework
  async fetchAll(): Promise<FetchResult | null> {
    try {
      const response = await apiClient.get<FetchResult>(BASE_URL);
      return response.data ?? null;
    } catch (error) {
      console.error("Error fetching frameworks:", error);
      return null;
    }
  },

  /**
 * GET - Fetch a single framework by its ID.
 * @param frameworkId - The ID of the framework to fetch.
 */
  async getById(frameworkId: number): Promise<Framework> {
    try {
      const response = await apiClient.get<{ Record: Framework }>(`${BASE_URL}/${frameworkId}`);
      return response.data.Record ?? null;
    } catch (error) {
      console.error(`Error fetching framework with ID ${frameworkId}:`, error);
      throw error;
    }
  },

  // POST - Create a new framework
  async create(framework: Partial<Framework>): Promise<Framework | null> {
    try {
      const response = await apiClient.post<Framework>(BASE_URL, framework);
      return response.data ?? null;
    } catch (error) {
      console.error("Error creating framework:", error);
      return null;
    }
  },

  // PUT - Update an existing framework
  async update(frameworkId: number, updatedFramework: Partial<Framework>): Promise<Framework | null> {
    try {
      const response = await apiClient.put<Framework>(`${BASE_URL}/${frameworkId}`, updatedFramework);
      return response.data ?? null;
    } catch (error) {
      console.error("Error updating fiscal year:", error);
      return null;
    }
  },

  // DELETE - Remove a framework
  async delete(frameworkId: number): Promise<boolean> {
    try {
      await apiClient.delete(`${BASE_URL}/${frameworkId}`);
      return true;
    } catch (error) {
      console.error("Error deleting framework:", error);
      return false;
    }
  }
};
