import React, { useState, useEffect } from "react";
import CommentIcon from "./components/CommentIcon";
import CommentPopup from "./components/CommentPopUp";
// import commentService from "../services/commentService"; // import service here
import styles from "./CommentPopup.module.css";

const CommentManager: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [statusOpen, setStatusOpen] = useState(true);
  const [commentText, setCommentText] = useState("");
  const [chatList, setChatList] = useState<any[]>([]);

  const togglePopup = () => setIsOpen(!isOpen);
  const toggleStatus = () => setStatusOpen(!statusOpen);

  const fetchComments = async () => {
    try {
      // const data = await commentService.list(); // call directly
      setChatList([]); // set data to chatList
    } catch (err) {
      console.error("Failed to fetch comments", err);
    }
  };

  const handleSubmit = async () => {
    if (commentText.trim()) {
      try {
        // await commentService.create(commentText); 
        fetchComments();
        setCommentText("");
      } catch (err) {
        console.error("Failed to post comment", err);
      }
    }
  };

  const handleCancel = () => {
    if (!commentText) togglePopup();
    setCommentText("");
  };

  useEffect(() => {
    fetchComments();
  }, []);

  return (
    <div className={styles.container}>
      <CommentIcon onClick={togglePopup} />
      {isOpen && (
        <CommentPopup
          statusOpen={statusOpen}
          toggleStatus={toggleStatus}
          chatList={chatList}
          commentText={commentText}
          setCommentText={setCommentText}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
        />
      )}
    </div>
  );
};

export default CommentManager;
