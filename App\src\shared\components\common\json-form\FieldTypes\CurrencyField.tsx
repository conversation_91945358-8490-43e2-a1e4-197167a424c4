import React, { useState, useEffect } from "react";
import { FormField } from "../types";
import styles from "./styles.module.css";

interface CurrencyFieldProps {
  field: FormField;
  value: string | number;
  onChange: (name: string, value: string) => void;
  currencySymbol?: string;
  precision?: number;
}

const CurrencyField: React.FC<CurrencyFieldProps> = ({
  field,
  value,
  onChange,
  currencySymbol = "$",
  precision = 2,
}) => {
  const [inputValue, setInputValue] = useState("");

  useEffect(() => {
    // On mount, set raw value without formatting
    if (value !== undefined && value !== null) {
      setInputValue(String(value));
    }
  }, [value]);

  const formatCurrency = (amount: string | number) => {
    const numericValue = parseFloat(String(amount));
    if (!isNaN(numericValue)) {
      return `${currencySymbol} ${numericValue.toLocaleString(undefined, {
        minimumFractionDigits: precision,
        maximumFractionDigits: precision,
      })}`;
    }
    return `${currencySymbol} 0.00`;
  };

  const handleFocus = () => {
    setInputValue(String(value)); // Show raw input
  };

  const handleBlur = () => {
    if (inputValue !== "") {
      setInputValue(formatCurrency(inputValue)); // Format after blur
    }
  };

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const rawValue = event.target.value.replace(/[^0-9.]/g, ""); // Allow numbers & decimals only
    setInputValue(rawValue); // Store raw input while typing
    onChange(field.name, rawValue); // Update form state
  };

  return (
    <div className={styles.currencyField}>
      <input
        type="text"
        id={field.name}
        name={field.name}
        value={inputValue}
        className={styles.input}
        placeholder={`${currencySymbol} 0.00`}
        onChange={handleChange}
        onFocus={handleFocus} // Show raw input
        onBlur={handleBlur} // Format input after blur
      />
    </div>
  );
};

export default CurrencyField;
