import React, { useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useAuth } from "../../../providers/auth/AuthContext";
import { Button } from "../../../shared/components/common";

const Login: React.FC = () => {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);

  const { login } = useAuth();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setLoading(true);

    if (!username || !password) {
      setError("Please fill in both fields.");
      return;
    }

    try {
      setError("");
      await login({ username, password });

      // Get the returnUrl from query parameters or default to "/"
      const returnUrl = searchParams.get("returnUrl") || "/";
      // Redirect to return url
      navigate(returnUrl);
    } catch (error) {
      if (error instanceof Error) {
        setError(error.message);
      } else {
        setError("An unexpected error occurred.");
      }
    }

    setLoading(false);
  };

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="w-full max-w-sm p-[48px] bg-white rounded-xl shadow-lg">
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold text-gray-800">Sign In</h1>
        </div>
        <form onSubmit={handleSubmit} className="space-y-6 text-sm">
          <div>
            <label htmlFor="username" className="block text-sm font-medium text-gray-700">
              Username
            </label>
            <input
              type="text"
              id="username"
              name="username"
              className="mt-0.5 w-full py-0.5 border-b focus:outline-none"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              required
              autoComplete="username"
              autoFocus
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700">
              Password
            </label>
            <input
              type="password"
              id="password"
              name="password"
              className="mt-0.5 w-full py-0.5 border-b focus:outline-none"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              autoComplete="current-password"
            />
          </div>

          <div className="flex text-sm text-gray-600">
            <label className="flex items-center">
              <input type="checkbox" className="mr-2" /> Remember Me
            </label>
          </div>

          {error && <p className="text-sm text-red-600 font-medium">{error}</p>}

          <Button
            type="submit"
            className="flex justify-center gap-2 w-full py-2 px-4 bg-[#2f99e0] text-white font-semibold rounded-md hover:shadow-lg transition duration-200"
            loader={loading}
          >
            Login
          </Button>
        </form>

        <div className="flex justify-end text-sm mt-4 text-gray-600">
          <a href="#" className="hover:underline text-gray-600">
            Forgot Password?
          </a>
        </div>
      </div>
    </div>
  );
};

export default Login;
