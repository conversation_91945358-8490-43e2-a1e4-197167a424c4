import apiClient from "../../network/apiClient";

// Define user structure
export interface Event {
  Id: number;
  Code: string;
  Name: string;
  Description: string;
  EventTypeId: number;
  CreatedById?: number | null;
  CreatedAt?: string | null;
  UpdatedById?: number | null;
  UpdatedAt?: string | null;
  IsDeleted: boolean;
}

interface EventTypeId {
  Value: number;
  DisplayText: string;
  DisplayCode?: string | null;
  Active: number;
  GroupId?: number | null;
  GroupName?: string | null;
}

// Define fetch-all response structure
interface FetchResult {
  Record: Event[];
  EventTypeId: EventTypeId[];
  UserRights: string;
}

const BASE_URL = `${import.meta.env.VITE_PGM_API_BASE_URL}/api/events`;

export const eventService = {
  // GET - Fetch all events
  async fetchAll(): Promise<FetchResult | null> {
    try {
      const response = await apiClient.get<FetchResult>(BASE_URL);
      return response.data ?? null;
    } catch (error) {
      console.error("Error fetching events:", error);
      return null;
    }
  },

  /**
 * GET - Fetch a single event by its ID.
 * @param eventId - The ID of the event to fetch.
 */
  async getById(eventId: number): Promise<Event> {
    try {
      const response = await apiClient.get<{ Record: Event }>(`${BASE_URL}/${eventId}`);
      return response.data.Record ?? null;
    } catch (error) {
      console.error(`Error fetching event with ID ${eventId}:`, error);
      throw error;
    }
  },

  // POST - Create a new event
  async create(event: Partial<Event>): Promise<Event | null> {
    try {
      const response = await apiClient.post<Event>(BASE_URL, event);
      return response.data ?? null;
    } catch (error) {
      console.error("Error creating event:", error);
      return null;
    }
  },

  // PUT - Update an existing event
  async update(eventId: number, updatedEvent: Partial<Event>): Promise<Event | null> {
    try {
      const response = await apiClient.put<Event>(`${BASE_URL}/${eventId}`, updatedEvent);
      return response.data ?? null;
    } catch (error) {
      console.error("Error updating event:", error);
      return null;
    }
  },

  // DELETE - Remove a event
  async delete(eventId: number): Promise<boolean> {
    try {
      await apiClient.delete(`${BASE_URL}/${eventId}`);
      return true;
    } catch (error) {
      console.error("Error deleting event:", error);
      return false;
    }
  }
};
