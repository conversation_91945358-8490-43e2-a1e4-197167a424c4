import React from "react";
import { FormField } from "../types";

interface SelectFieldProps {
  field: FormField;
  value: string | number;
  onChange: (name: string, value: string | number) => void;
}

const SelectField: React.FC<SelectFieldProps> = ({ field, value, onChange }) => {
  return (
    <div>
      <select
        name={field.name}
        required={field.required}
        value={value}
        onChange={(e) => onChange(field.name, e.target.value)}
      >
        {/* Default placeholder option */}
        <option value="" disabled>
          {`Select ${field.label}`}
        </option>

        {(Array.isArray(field.options) ? field.options : [])?.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  );
};

export default SelectField;
