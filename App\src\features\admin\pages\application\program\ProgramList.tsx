import React from "react";
import { MasterPage } from "../../../../../shared/components/render-elements";
import { ColumnSchema } from "../../../../../shared/components/common/data-table/types";

export const programSchema: ColumnSchema[] = [
  {
    key: "Id",
    title: "Id",
    hidden: true,
    sorter: false
  },
  { key: "Code", title: "Code", editable: true, sorter: true, type: "text", filterable: true },
  { key: "Title", title: "Title", editable: true, sorter: true, type: "text", filterable: true },
  { key: "Description", title: "Description", editable: true, type: "text", filterable: true }
];

const ProgramList: React.FC = () => {

  return <MasterPage
    mode='form'
    entityName="Program"
    columnsSchema={programSchema}
    apiEndpoints={{
      create: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/programs`,
      edit: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/programs`,
      delete: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/programs`,
      list: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/programs`
    }}
    routeTemplates={{
        create: "/admin/application/programs/create",
        edit: "/admin/application/programs/:id/edit",
    }}
  />;
}

export default ProgramList;
