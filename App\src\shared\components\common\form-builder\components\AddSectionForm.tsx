import React, { useState } from 'react';
import { Modal } from 'antd';

interface AddSectionProps {
    level: number;
    language: string;
    showModal: boolean;
    onCancel: () => void;
    onSubmit: (e: Record<string, string>) => void;
}

const AddSectionForm: React.FC<AddSectionProps> = ({
    showModal,
    language,
    level,
    onCancel,
    onSubmit
}) => {

    const [title, setTitle] = useState<Record<string, string>>({});

    return <Modal
    closable={true}
    open={showModal}
    onOk={() => {
      onCancel();
      setTitle({});
    }}
    onCancel={() => {
        onCancel();
        setTitle({});
    }}
    footer={null}
    centered
    destroyOnClose
  >
    {/* header */}
    <div className='text-[16px] font-bold text-[#0b233f]'>
        { `Add ${level == 1? 'Section': 'Sub Section' }` }
    </div>

    {/* title */}
    <input
        type="text"
        className="border p-2 w-full rounded mb-4 mt-[30px]"
        value={title?.[language]}
        onChange={(e) => {
            const objTitle = {...title};
            objTitle[language] = e.target.value;
            setTitle(objTitle);
        }}
    />

    {/* buttons */}
    <div className="flex justify-end space-x-2 mt-[50px]">
        <button onClick={() => {
            onSubmit(title);
            setTitle({});
            }} 
            className="min-w-[100px] border border-[#0b233f] bg-[#0b233f] h-[30px] text-[14px] font-medium text-white cursor-pointer mr-[10px]">
            Add
        </button>
    </div>
    
  </Modal>
}

export default AddSectionForm;