export const getWidgetsByType = (dataType: string) => {
    switch (dataType.toLowerCase()) {
        case 'string':
        case 'varchar':
        case 'text':
            return [
                {value: 'text', label: 'Textbox'},
                {value: 'textarea', label: 'Textarea'},
                {value: 'number', label: 'Number'},
                {value: 'hidden', label: 'Hidden'},
                {value: 'email', label: 'Email'},
                {value: 'password', label: 'Password'},
                {value: 'currency', label: 'Currency'},
                {value: 'select', label: 'Select'},
                {value: 'autocomplete', label: 'Autocomplete'},
                {value: 'multiselect', label: 'Multiselect'},
                {value: 'checkbox', label: 'Checkbox'},
                {value: 'radio', label: 'Radio'},
                {value: 'file', label: 'File'},
                {value: 'widget', label: 'Widget'},
                {value: 'masked', label: 'Masked'},
                {value: 'toggle', label: 'Toggle'},
                {value: 'rating', label: 'Rating'}
            ];
        case 'integer':
            return [
                {value: 'number', label: 'Number'},
                {value: 'hidden', label: 'Hidden'},
                {value: 'select', label: 'Select'},
                {value: 'autocomplete', label: 'Autocomplete'},
                {value: 'checkbox', label: 'Checkbox'},
                {value: 'radio', label: 'Radio'},
                {value: 'rating', label: 'Rating'}
            ]
        case 'integer-array':
            return [
                {value: 'multiselect', label: 'Multiselect'}
            ]
        case 'date':
            return [
                {value: 'date', label: 'Date'}
            ]
        default:
            return [{value: 'text', label: 'Textbox'}]
    }
}