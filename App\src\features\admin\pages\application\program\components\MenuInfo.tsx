import React from "react";
import { DownOutlined, UpOutlined } from "@ant-design/icons";

type ModuleOption = {
    value: string;
    label: string;
};

type MenuItem = {
    value: string;
    DisplayText: string;
    GroupId: string;
};

type MenuData = {
    [groupId: string]: {
        [menuText: string]: { checked: boolean; displayName: string };
    };
};

type MenuInfoProps = {
    moduleOptions: ModuleOption[];
    menuItems: MenuItem[];
    openTab: string | null;
    setOpenTab: (value: string | null) => void;
    menuData: MenuData;
    setMenuData: React.Dispatch<React.SetStateAction<MenuData>>;
};

const MenuInfo: React.FC<MenuInfoProps> = ({ moduleOptions, menuItems, openTab, setOpenTab, menuData, setMenuData }) => {

    const handleTabClick = (value: string) => {
        setOpenTab(openTab === value ? null : value);
    };

    const groupedMenuOptions = menuItems.reduce((acc, item) => {
        if (!acc[item.GroupId]) acc[item.GroupId] = [];
        acc[item.GroupId].push(item.DisplayText);
        return acc;
    }, {} as Record<string, string[]>);

    const handleCheckboxChange = (groupId: string, item: string) => {
        setMenuData((prev) => {
            const isChecked = !prev[groupId]?.[item]?.checked;
            return {
                ...prev,
                [groupId]: {
                    ...prev[groupId],
                    [item]: {
                        ...prev[groupId]?.[item],
                        checked: isChecked,
                        displayName: isChecked ? item : '',
                    },
                },
            };
        });
    };

    const handleDisplayNameChange = (groupId: string, item: string, value: string) => {
        setMenuData((prev) => ({
            ...prev,
            [groupId]: {
                ...prev[groupId],
                [item]: {
                    ...prev[groupId]?.[item],
                    displayName: value,
                },
            },
        }));
    };

    return (
        <div className="p-[20px] w-[1000px]">
            <h2 className="text-[20px] leading-[20px] text-[#0B3456] font-semibold mb-4">Program Menu</h2>
            <div className="flex flex-col gap-[20px]">
                {moduleOptions.map((option) => (
                    <div key={option.value} className="border border-[#0B3456] rounded-[10px]">
                        <div
                            className={`w-full bg-[#E6F7FF] text-[#0B3456] font-medium p-3 cursor-pointer hover:bg-[#B3E5FC] flex justify-between items-center ${openTab === option.value ? 'rounded-t-[10px]' : 'rounded-[10px]'}`}
                            onClick={() => handleTabClick(option.value)}
                        >
                            <span>{option.label}</span>
                            {openTab === option.value ? <UpOutlined /> : <DownOutlined />}
                        </div>

                        {openTab === option.value && (
                            <div className="px-[12px] py-2 space-y-2 border-t">
                                {/* Headings */}
                                <div className="flex items-center gap-2 font-semibold text-[#0B3456] mb-1">
                                    <div className="w-1/2">Menu</div>
                                    <div className="w-1/2">Display Name</div>
                                </div>
                                <hr className="mb-[20px] border border-gray-300" />
                                {/* Menu items */}
                                {groupedMenuOptions[option.value]?.map((subOption, idx) => (
                                    <div key={idx} className="flex items-center gap-2 mb-[20px]">
                                        <label className="w-1/2 flex items-center gap-2">
                                            <input
                                                type='checkbox'
                                                className='w-[15px] h-[15px]'
                                                checked={menuData[option.value]?.[subOption]?.checked || false}
                                                onChange={() => handleCheckboxChange(option.value, subOption)}
                                            />
                                            <span>{subOption}</span>
                                        </label>
                                        <input
                                            type='text'
                                            placeholder='Enter display name'
                                            className='w-[40%] h-[40px] px-2 border border-gray-300 rounded'
                                            value={menuData[option.value]?.[subOption]?.displayName || ''}
                                            onChange={(e) => handleDisplayNameChange(option.value, subOption, e.target.value)}
                                        />
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                ))}
            </div>
        </div>
    );
};

export default MenuInfo;