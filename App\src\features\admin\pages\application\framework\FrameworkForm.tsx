import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { FormOption, JForm, JFormSchema } from "../../../../../shared/components/common/json-form";
import { message } from "antd";
import { Loader } from "../../../../../shared/components/common";
import { Framework, frameworkService } from "../../../../../api/services/admin/frameworkService";

const FrameworkForm: React.FC<{ mode: "create" | "edit" }> = ({ mode }) => {
  const navigate = useNavigate();
  const { id } = useParams(); // Get framework ID from URL params for edit mode
  const [formData, setFormData] = useState<Framework | null>(null);
  const frameworkId = Number(id);

  const schema: JFormSchema = {
    title: `${mode} Framework`,
    formGroups: [
      {
        title: "",
        columns: 2,
        fields: [
          {
            name: "Id",
            type: "hidden",
            required: true,
            defaultValue: 0
          },
          {
            name: "Name",
            type: "text",
            label: "Name",
            required: true,
            validation: {
              required: true,
              maxLength: 50
            }
          },
          {
            name: "FrameworkTypeId",
            type: "select",
            label: "Framework Type",
            required: true,
            options: async (): Promise<FormOption[]> => {
              const response = await frameworkService.fetchAll();
              return response!.FrameworkTypeId.map(frameworkType => ({
                label: frameworkType.DisplayText,
                value: frameworkType.Value
              }));
            },
          }
        ]
      }
    ]
  };

  useEffect(() => {
        if (mode === "edit" && frameworkId) {
          frameworkService.getById(frameworkId)
            .then(setFormData)
            .catch((error) => console.error("Error fetching framework data:", error));
        }
      }, [mode, frameworkId]);
     
  const handleSubmit = async (formValues: Record<string, any>) => {
    try {
      const response = mode === "edit" && frameworkId
        ? await frameworkService.update(frameworkId, formValues)
        : await frameworkService.create(formValues);
  
      if (response) {
        message.success(`Framework ${mode === "edit" ? "updated" : "created"} successfully.`);
        navigate("..");
      } else {
        message.error(`Failed to ${mode === "edit" ? "update" : "create"} framework. Please try again.`);
      }
    } catch (error: any) {
      console.error("Error submitting form:", error);
      message.error(error?.response?.data?.message || "An unexpected error occurred.");
    }
  };

  return (
    <div className="flex flex-col gap-2 bg-white p-[30px] rounded-[6px]">
      {mode === "edit" && !formData ? (
        <Loader />
      ) : (
        <JForm schema={schema} onSubmit={handleSubmit} data={formData || {}} />
      )}
    </div>
  );
};

export default FrameworkForm;
