import apiClient from "../../network/apiClient";

// Define user structure
export interface User {
  Id: number;
  UserName: string;
  LoginName: string;
  NormalizedLoginName: string;
  Email: string;
  NormalizedEmail: string;
  PasswordHash: string;
  RoleId: number;
  EffectiveFrom: string;
  EffectiveTo: string;
  IsActive: boolean;
  IsLocked: boolean;
  CreatedById?: number | null;
  CreatedAt?: string | null;
  UpdatedById?: number | null;
  UpdatedAt?: string | null;
  IsDeleted: boolean;
}

// Define fetch-all response structure
interface FetchResult {
  Record: User[];
  UserRights: string;
}

const BASE_URL = `${import.meta.env.VITE_Admin_API_BASE_URL}/api/users`;

export const userService = {
  // GET - Fetch all users
  async fetchAll(): Promise<FetchResult | null> {
    try {
      const response = await apiClient.get<FetchResult>(BASE_URL);
      return response.data ?? null;
    } catch (error) {
      console.error("Error fetching users:", error);
      return null;
    }
  },

  /**
 * GET - Fetch a single user by its ID.
 * @param userId - The ID of the user to fetch.
 */
  async getById(userId: number): Promise<User> {
    try {
      const response = await apiClient.get<{ Record: User }>(`${BASE_URL}/${userId}`);
      return response.data.Record ?? null;
    } catch (error) {
      console.error(`Error fetching user with ID ${userId}:`, error);
      throw error;
    }
  },

  // POST - Create a new user
  async create(user: Partial<User>): Promise<User | null> {
    try {
      const response = await apiClient.post<User>(BASE_URL, user);
      return response.data ?? null;
    } catch (error) {
      console.error("Error creating user:", error);
      return null;
    }
  },

  // PUT - Update an existing user
  async update(userId: number, updatedUser: Partial<User>): Promise<User | null> {
    try {
      const response = await apiClient.put<User>(`${BASE_URL}/${userId}`, updatedUser);
      return response.data ?? null;
    } catch (error) {
      console.error("Error updating user:", error);
      return null;
    }
  },

  // DELETE - Remove a user
  async delete(userId: number): Promise<boolean> {
    try {
      await apiClient.delete(`${BASE_URL}/${userId}`);
      return true;
    } catch (error) {
      console.error("Error deleting user:", error);
      return false;
    }
  }
};
