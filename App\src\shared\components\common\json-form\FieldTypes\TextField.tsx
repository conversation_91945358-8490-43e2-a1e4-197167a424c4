import React from "react";
import { FormField } from "../types";

interface TextFieldProps {
  field: FormField;
  value: string;
  onChange: (name: string, value: string) => void;
}

const TextField: React.FC<TextFieldProps> = ({ field, value, onChange }) => {
  return (
    <div>
      <input
        type={field.type}
        name={field.name}
        // required={field.required}
        value={value}
        onChange={(e) => onChange(field.name, e.target.value)}
      />
    </div>
  );
};

export default TextField;
