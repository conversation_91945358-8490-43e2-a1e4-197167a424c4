import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import ModuleCard from "../components/ModuleCard";
import { ModuleGroup } from "../../../api/services/common/appSettingService";
import { useMenu } from "../../../providers/MenuProvider";

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const modules = useMenu();

  const [data, setData] = useState<ModuleGroup[]>([]);

  useEffect(() => {
    setData(modules?.ModuleGroup ?? []);
  }, [modules]);

  return (
    <div className="flex flex-wrap justify-center gap-6 p-6">
      {data.map((mod) => (
        <ModuleCard
          key={mod.Id}
          module={mod}
          onSelect={() => { navigate(`${mod.URL}`) }}
        >
        </ModuleCard>
      ))}
    </div>
  )
};

export default Dashboard;
