import React from "react";
import { MasterPage } from "../../../../shared/components/render-elements";
import { ColumnSchema } from "../../../../shared/components/common/data-table/types";

interface UserGroupMappingListProps {
  userGroupId: string;
}

const userGroupMappingSchema: ColumnSchema[] = [
  {
    key: "Id",
    title: "Id",
    hidden: true,
    editable: false,
    sorter: false
  },
  {
    key: "UserId",
    title: "User",
    editable: true,
    sorter: true,
    type: "select",
    filterable: true
  },
  {
    key: "DefaultUserGroup",
    title: "Default User Group",
    editable: true,
    sorter: true,
    filterable: true,
    type: "select",
    options: [
      { value: 1, label: "Yes" },
      { value: 0, label: "No" }
    ]
  }
];

const UserGroupMappingList: React.FC<UserGroupMappingListProps> = ({ userGroupId }) => {
  return (
    <MasterPage
      entityName="User Group Mapping"
      columnsSchema={userGroupMappingSchema}
      mode="modal"
      modal={{
        formSchema: {
          title: "User Group Mapping Management",
          formGroups: [
            {
              title: "",
              columns: 2,
              fields: [
                {
                  name: "UserGroupsId",
                  type: "hidden",
                  required: true,
                  defaultValue: userGroupId
                },
                {
                  name: "UserId",
                  type: "select",
                  label: "User",
                  required: true
                },
                {
                  name: "DefaultUserGroup",
                  type: "select",
                  label: "Default User Group",
                  required: true,
                  options: [
                    { value: 1, label: "Yes" },
                    { value: 0, label: "No" }
                  ]
                }
              ]
            }
          ]
        },
        width: 50
      }}
      apiEndpoints={{
        create: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/user-group-mapping`,
        edit: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/user-group-mapping`,
        delete: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/user-group-mapping`,
        list: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/user-group-mapping?Id=${userGroupId}`
      }}
    />
  );
};

export default UserGroupMappingList;
