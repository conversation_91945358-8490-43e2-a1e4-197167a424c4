import React from "react";

type BasicInfoProps = {
    basicInfo: { code: string; name: string; description: string };
    setBasicInfo: React.Dispatch<React.SetStateAction<{ code: string; name: string; description: string }>>;
};

const fields = [
    { label: "Code", name: "code", placeholder: "Enter code" },
    { label: "Name", name: "name", placeholder: "Enter name" },
    { label: "Description", name: "description", placeholder: "Enter description" }
];

const BasicInfo: React.FC<BasicInfoProps> = ({ basicInfo, setBasicInfo }) => {
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setBasicInfo(prev => ({ ...prev, [name]: value }));
    };

    return (
        <div className="p-[20px] w-[800px]">
            <h2 className="text-[20px] leading-[20px] text-[#0B3456] font-semibold mb-4">Program Info</h2>
            <div className="space-y-4">
                {fields.map((field, index) => (
                    <div key={index} className="flex items-center">
                        <label className="w-40 font-medium text-[#0B3456]">{field.label}:</label>
                        <input
                            type="text"
                            name={field.name}
                            className="flex-1 rounded px-3 py-2"
                            placeholder={field.placeholder}
                            value={(basicInfo as any)[field.name]}
                            onChange={handleChange}
                        />
                    </div>
                ))}
            </div>
        </div>
    );
};

export default BasicInfo;
