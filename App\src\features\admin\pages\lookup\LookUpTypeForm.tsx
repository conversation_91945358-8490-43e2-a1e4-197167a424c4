import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { J<PERSON><PERSON>, JFormSchema } from "../../../../shared/components/common/json-form";
import { message } from "antd";
import { Loader } from "../../../../shared/components/common";
import LookUpList from "../lookup/LookUpList";
import {
  lookupTypeService,
  LookUpType
} from "../../../../api/services/admin/lookUpTypeService";

const LookUpTypeForm: React.FC<{ mode: "create" | "edit" }> = ({ mode }) => {
  const navigate = useNavigate();
  const { id } = useParams();
  const LookUpTypeId = id ? Number(id) : null;
  const [formData, setFormData] = useState<LookUpType | null>(null);

  const schema: JFormSchema = {
    title: `${mode === "edit" ? "Edit" : "Create"} Lookup Type`,
    formGroups: [
      {
        title: "",
        columns: 2,
        fields: [
          {
            name: "Id",
            type: "hidden",
            required: true,
            defaultValue: 0
          },
          {
            name: "FieldName",
            type: "text",
            label: "Name",
            required: true,
            validation: {
              required: true,
              maxLength: 100
            }
          },
          {
            name: "Description",
            type: "text",
            label: "Description",
            required: false,
            validation: {
              maxLength: 500
            }
          },
         
          {
            name: "IsSystemDefined",
            type: "toggle",
            label: "IsSystemDefined",
            required: true,
            transform: (value) => (value ? 1 : 0),
            defaultValue: 1,
            validation: {
              required: true,
              custom: (value) => {
                const num = Number(value)
                return num === 0 || num === 1 ? undefined : "Value must be 0 or 1";
              },
            }
          },
        ]
      }
    ]
  };

  useEffect(() => {
    if (mode === "edit" && LookUpTypeId !== null) {
      lookupTypeService
        .getById(LookUpTypeId)
        .then(setFormData)
        .catch((error) => console.error("Error fetching lookup type data:", error));
    }
  }, [mode, LookUpTypeId]);

  const handleSubmit = async (formValues: Record<string, any>) => {
    try {
      const response =
        mode === "edit" && LookUpTypeId !== null
          ? await lookupTypeService.update(LookUpTypeId, formValues)
          : await lookupTypeService.create(formValues);

      if (response) {
        message.success(`Lookup type ${mode === "edit" ? "updated" : "created"} successfully.`);
        navigate("..");
      } else {
        message.error(`Failed to ${mode === "edit" ? "update" : "create"} lookup type. Please try again.`);
      }
    } catch (error: any) {
      console.error("Error submitting form:", error);
      message.error(error?.response?.data?.message || "An unexpected error occurred.");
    }
  };

  return (
    <>
      <div className="flex flex-col gap-2 bg-white p-[30px] rounded-[6px]">
        {mode === "edit" && !formData ? (
          <Loader />
        ) : (
          <JForm schema={schema} onSubmit={handleSubmit} data={formData || {}} />
        )}
      </div>

      <div className="mt-[15px]">
        {LookUpTypeId !== null && <LookUpList LookUpTypeId={LookUpTypeId} />}
      </div>
    </>
  );
};

export default LookUpTypeForm;
