import React from "react";

interface HomeIconProps {
    className?: string;
    isActive?: boolean;
}

const HomeIcon: React.FC<HomeIconProps> = ({ className, isActive, ...rest }) => {
    return (
        <div className={className} style={{ cursor: "pointer" }} {...rest}>
            {isActive ? (
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="25.5px"
                    height="25.5px"
                    viewBox="0 0 25.5 25.5"
                >
                    <g>
                        <g>
                            <path
                                fill="#0B233F"
                                d="M21.2,25.2h-17c-2.2,0-4-1.8-4-4v-17c0-2.2,1.8-4,4-4h17c2.2,0,4,1.8,4,4v17C25.2,23.5,23.5,25.2,21.2,25.2z"
                            />
                            <polygon
                                fill="#00AEEF"
                                points="11.1,20.8 11.1,15.1 14.4,15.1 14.4,20.8 18.4,20.8 18.4,13.2 20.8,13.2 12.8,4.8 4.7,13.2 7.1,13.2 7.1,20.8"
                            />
                        </g>
                    </g>
                </svg>
            ) : (
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="25.5px"
                    height="25.5px"
                    viewBox="0 0 25.5 25.5"
                >
                    <g>
                        <path
                            fill="#00AEEF"
                            d="M21.2,25.2h-17c-2.2,0-4-1.8-4-4v-17c0-2.2,1.8-4,4-4h17c2.2,0,4,1.8,4,4v17C25.2,23.5,23.5,25.2,21.2,25.2z"
                        />
                        <polygon
                            fill="#0B233F"
                            points="11.1,20.8 11.1,15.1 14.4,15.1 14.4,20.8 18.4,20.8 18.4,13.2 20.8,13.2 12.8,4.8 4.7,13.2 7.1,13.2 7.1,20.8"
                        />
                    </g>
                </svg>
            )}
        </div>
    );
};

export default HomeIcon;
