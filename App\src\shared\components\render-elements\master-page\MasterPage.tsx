import { useCallback, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Modal } from "antd";
import { toast } from "react-toastify";
import {
  ActionButton,
  ColumnSchema,
  DataTable,
  OptionItem,
} from "../../common/data-table";
import createApiService from "../../../../api/services/common/masterPageService";
import { JForm, JFormSchema } from "../../common/json-form";

interface MasterPageProps<T extends { Id: string }> {
  entityName: string;
  columnsSchema: ColumnSchema[];
  mode: "inline" | "form" | "modal";
  modal?: {
    formSchema: JFormSchema;
    width?: number;
  };
  apiEndpoints: {
    create?: string;
    edit?: string;
    delete?: string;
    list?: string;
  };
  routeTemplates?: {
    create: string;// Standardized create route
    edit: string;// Route template with `:id` placeholder
  };
  customActions?: ActionButton<T>[];
}

const MasterPage = <T extends { Id: string }>({
  entityName,
  columnsSchema,
  apiEndpoints,
  mode = "inline",
  modal,
  routeTemplates,
  customActions,
}: MasterPageProps<T>) => {
  // Initialize API service
  const apiService = createApiService<T>(apiEndpoints);
  const [data, setData] = useState<Array<T & { key: string }>>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editRecord, setEditRecord] = useState<T | null>(null);
  const [options, setOptions] = useState<{ [key: string]: OptionItem[] }>({});
  const navigate = useNavigate();

  useEffect(() => {
    loadData();
  }, []);

  const loadData = useCallback(async () => {
    try {
      const fetchedData = await apiService.fetchAll();
      const recordsArray = fetchedData.Record;

      const mappedData = recordsArray.map((item) => ({
        ...item,
        key: item.Id,// Ensure 'key' exists for Ant Design Table
      }));

      const matchingKeys = columnsSchema
        .map((col) => col.key)
        .filter((key) => fetchedData.hasOwnProperty(key));

      const extractedOptions: { [key: string]: OptionItem[] } = {};
      matchingKeys.forEach((key) => {
        if (Array.isArray(fetchedData[key])) {
          extractedOptions[key] = fetchedData[key].map((item: any) => ({
            value: item.Value,// Ensures 'value' is properly formatted
            label: item.DisplayText || item.Value.toString(),// Uses DisplayText when available
          }));
        }
      });

      setOptions(extractedOptions);
      setData(mappedData);
    } catch (error) {
      toast.error(`Failed to load ${entityName} data`);
      console.error(error);
    }
  }, [apiService, columnsSchema, entityName]);

  const handleAdd = async () => {
    if (mode === "form") {
      if (!routeTemplates?.create) throw new Error("Missing create route.");
      navigate(routeTemplates.create);
    } else if (mode === "modal") {
      setEditRecord(null);
      setIsModalVisible(true);// Show modal instead of redirecting
    }
  };

  const handleCancel = () => {
    setIsModalVisible(false);
     // Clear edit record on cancel so that subsequent opens default to creation mode
    setEditRecord(null);
  };

  const handleEdit = async (key: string) => {
    if (mode === "inline") {
      // await apiService.update(key, newData);
      loadData();
    } else if (mode === "form") {
      if (!routeTemplates?.edit.includes(":id")) {
        throw new Error("Invalid edit route: Missing ':id' placeholder.");
      }
      navigate(routeTemplates.edit.replace(":id", key));
    } else if (mode === "modal") {
       // Find the record to edit and open the modal
      const recordToEdit = data.find((item) => item.Id === key) || null;
      setEditRecord(recordToEdit);
      setIsModalVisible(true);
    }
  };

  const handleDelete = async (key: string) => {
    try {
      await apiService.delete(key);
      toast.success(`${entityName} deleted successfully`);
      await loadData();
    } catch (error) {
      toast.error(`Failed to delete ${entityName}`);
      console.error(error);
    }
  };

  const handleSave = async (
    key: string,
    editMode: boolean,
    updatedData: any,
  ) => {
    try {
      if (editMode) {
        await apiService.update(key, updatedData);
        toast.success(`${entityName} updated successfully`);
      } else {
        await apiService.create(updatedData);
        toast.success(`${entityName} created successfully`);
      }

      await loadData();
      setIsModalVisible(false);
      setEditRecord(null);
    } catch (error) {
      toast.error(`Failed to ${editMode ? "update" : "create"} ${entityName}`);
      console.error(error);
    }
  };

  const handleModalSave = async (updatedData: any) => {
    if (editRecord) {
      // Edit existing record
      await handleSave(editRecord.Id, true, updatedData);
    } else {
      // Create new record
      await handleSave("", false, updatedData);
    }
  };
 // Update column schema dynamically to include dropdown options
  const updatedColumnsSchema = columnsSchema.map((col) => ({
    ...col,
    options: col.options ? col.options : options[col.key] || [],
  }));

  const updatedModalFormSchema = modal?.formSchema
    ? {
        ...modal.formSchema,
        formGroups: modal.formSchema.formGroups?.map((group) => ({
          ...group,
          fields: group.fields?.map((field) => ({
            ...field,
            options: field.options ?? options[field.name] ?? [],// Use existing options or dynamically set
          })),
        })),
        fields: modal.formSchema.fields?.map((field) => ({
          ...field,
          options: field.options ?? options[field.name] ?? [],// Use existing options or dynamically set
        })),
      }
    : ({} as JFormSchema);

  return (
    <div className="flex flex-col gap-2 h-full bg-white p-[30px] rounded-[6px]">
       {/* Render modal only when mode is "modal" */}
      {mode === "modal" && modal?.formSchema && (
        <Modal
          open={isModalVisible}
          onCancel={handleCancel}
          footer={null}
          destroyOnClose
          width="auto"
          style={{ maxWidth: `${modal?.width}%` }}
        >
          <JForm
            schema={updatedModalFormSchema}
            onSubmit={handleModalSave}
            data={editRecord || {}}
          />
        </Modal>
      )}

      <DataTable
        header={`${entityName}`}
        data={data}
        columnsSchema={updatedColumnsSchema}
        inlineEditing={mode === "inline"}
        onAdd={handleAdd}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onSave={handleSave}
        customActions={customActions}
      />
    </div>
  );
};

export default MasterPage;
