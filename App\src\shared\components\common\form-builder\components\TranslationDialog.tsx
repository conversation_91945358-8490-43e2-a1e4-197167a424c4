import React, { useEffect, useState } from 'react';
import { CloseOutlined } from '@ant-design/icons';

interface TranslationDialogProps {
    label?: string;
    languageKey?: string;
    onClose: () => void;
    visible: boolean;
    onSubmit: (translations: { [key: string]: string }) => void;
    existingTranslations?: { [key: string]: string };
}

const languageMap: { [key: string]: string } = {
    en: "English",
    es: "Spanish",
    fr: "French"
};

const TranslationDialog: React.FC<TranslationDialogProps> = ({ label, languageKey, onClose, visible, onSubmit, existingTranslations = {} }) => {
    const [translations, setTranslations] = useState<{ [key: string]: string }>({});

    useEffect(() => {
        if (!visible) return;
        const initialState = Object.keys(languageMap).reduce((acc, key) => {
            acc[key] =
                existingTranslations[key] ??
                (key === languageKey && label ? label : "");
            return acc;
        }, {} as { [key: string]: string });

        setTranslations(initialState);
    }, [visible]);

    if (!visible) return null;

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        onSubmit(translations);
        onClose();
    };

    return (
        <div className="fixed inset-0 bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white border p-6 rounded-lg w-[500px] h-[500px] shadow-lg relative flex flex-col justify-between">
                <button
                    onClick={onClose}
                    className="absolute top-5 right-5 text-gray-500 hover:text-gray-700 text-xl"
                >
                    <CloseOutlined />
                </button>

                <form onSubmit={handleSubmit} className="flex flex-col justify-between h-full">
                    <div>
                        <h2 className="text-xl font-bold mb-[30px]">Translate</h2>
                        <div className="flex flex-col gap-4">
                            {Object.entries(languageMap).map(([key, language]) => (
                                <div className="mb-[30px]" key={key}>
                                    <label className="block font-medium mb-1">{language}</label>
                                    <input
                                        type="text"
                                        value={translations[key] || ''}
                                        onChange={(e) =>
                                            setTranslations((prev) => ({
                                                ...prev,
                                                [key]: e.target.value
                                            }))
                                        }
                                        className="w-full border rounded px-3 py-2"
                                    />
                                </div>
                            ))}
                        </div>
                    </div>

                    <div className="flex justify-end mt-4">
                        <button
                            type="submit"
                            className="px-6 py-2 rounded text-white font-semibold bg-[#00AEEF]"
                        >
                            Update
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default TranslationDialog;
