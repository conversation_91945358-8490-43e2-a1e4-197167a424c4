import apiClient from "../../network/apiClient";

// -----------------------------------
// Interfaces
// -----------------------------------

export interface LookUp {
  Id: number;
  LookUpTypeId: number;
  Code: string;
  Description: string;
  SortOrder: number;
  IsSystemDefined: boolean;
}

export interface LookUpType {
  Id: number;
  FieldName: string;
  Description: string;
  IsSystemDefined: boolean;
  LookUps?: LookUp[];
}

interface FetchResult {
  Record: LookUpType[];
  UserRights?: string;
}

// -----------------------------------
// API URLs
// -----------------------------------

const BASE_URL = `${import.meta.env.VITE_Admin_API_BASE_URL}/api/lookup-types`;
const LOOKUP_URL = `${import.meta.env.VITE_Admin_API_BASE_URL}/api/lookups`;

// -----------------------------------
// LookupType Service
// -----------------------------------

export const lookupTypeService = {
  async fetchAll(): Promise<FetchResult | null> {
    try {
      const response = await apiClient.get<FetchResult>(BASE_URL);
      return response.data ?? null;
    } catch (error) {
      console.error("Error fetching lookup types:", error);
      return null;
    }
  },

  async getById(id: number): Promise<LookUpType | null> {
    try {
      const response = await apiClient.get<{ Record: LookUpType }>(`${BASE_URL}/${id}`);
      return response.data?.Record ?? null;
    } catch (error) {
      console.error(`Error fetching lookup type with ID ${id}:`, error);
      return null;
    }
  },

  async create(data: Partial<LookUpType>): Promise<LookUpType | null> {
    try {
      const response = await apiClient.post<LookUpType>(BASE_URL, data);
      return response.data ?? null;
    } catch (error) {
      console.error("Error creating lookup type:", error);
      return null;
    }
  },

  async update(id: number, data: Partial<LookUpType>): Promise<LookUpType | null> {
    try {
      const response = await apiClient.put<LookUpType>(`${BASE_URL}/${id}`, data);
      return response.data ?? null;
    } catch (error) {
      console.error("Error updating lookup type:", error);
      return null;
    }
  },

  async delete(id: number): Promise<boolean> {
    try {
      await apiClient.delete(`${BASE_URL}/${id}`);
      return true;
    } catch (error) {
      console.error("Error deleting lookup type:", error);
      return false;
    }
  }
};

// -----------------------------------
// LookUp Service (Child Items)
// -----------------------------------

export const lookUpService = {
  async fetchByTypeId(lookupTypeId: number): Promise<LookUp[]> {
    try {
      const response = await apiClient.get<{ Record: LookUp[] }>(`${LOOKUP_URL}?Id=${lookupTypeId}`);
      return response.data?.Record ?? [];
    } catch (error) {
      console.error(`Error fetching lookups for type ID ${lookupTypeId}:`, error);
      return [];
    }
  },

  async create(data: Partial<LookUp>): Promise<LookUp | null> {
    try {
      const response = await apiClient.post<LookUp>(LOOKUP_URL, data);
      return response.data ?? null;
    } catch (error) {
      console.error("Error creating lookup:", error);
      return null;
    }
  },

  async update(data: LookUp): Promise<LookUp | null> {
    try {
      const response = await apiClient.put<LookUp>(LOOKUP_URL, data);
      return response.data ?? null;
    } catch (error) {
      console.error("Error updating lookup:", error);
      return null;
    }
  },

  async delete(id: number): Promise<boolean> {
    try {
      await apiClient.delete(`${LOOKUP_URL}/${id}`);
      return true;
    } catch (error) {
      console.error(`Error deleting lookup with ID ${id}:`, error);
      return false;
    }
  }
};
