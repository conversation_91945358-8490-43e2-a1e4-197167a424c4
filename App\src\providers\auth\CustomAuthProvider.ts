import { <PERSON>th<PERSON><PERSON>ider } from "./AuthProvider";
import { authService } from "../../api";
import { setAuthToken } from "../../api/utils/axiosInstance";
import { AuthCredential, AuthEventType, AuthUser } from "./authContext.types";

export class Custom<PERSON>uthProvider implements AuthProvider {
  async login(callback?: (eventType: string, data?: any) => void, credentials?: AuthCredential): Promise<void> {
    try {
      const response = await authService.login(credentials?.username ?? '', credentials?.password ?? '');

      localStorage.setItem("authToken", JSON.stringify(response));
      // Set token in Axios instance
      setAuthToken(response.access_token);

      if (callback) callback(AuthEventType.LOGIN_SUCCESS, response);
    } catch (error) {
      console.error("Custom login failed:", error);
      if (callback) callback(AuthEventType.LOGIN_FAILURE, error);
    }
  }

  async logout(callback?: (eventType: string) => void): Promise<void> {
    try {
      localStorage.removeItem("authToken");

      // Clear the token from Axios
      setAuthToken(null);

      if (callback) callback(AuthEventType.LOGOUT_SUCCESS);
    } catch (error) {
      console.error("Custom logout failed:", error);
      if (callback) callback(AuthEventType.LOGOUT_FAILURE);
    }
  }

  async isAuthenticated() {
    const token = localStorage.getItem("authToken");
    return token ? true : false;
  }

  async getUser(): Promise<AuthUser | null> {
    const token = localStorage.getItem("authToken");
    return token ? JSON.parse(token) : null; // Replace with actual user retrieval logic
  }
}
