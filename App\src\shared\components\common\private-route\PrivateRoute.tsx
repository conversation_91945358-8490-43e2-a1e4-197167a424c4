import React, { ReactNode } from "react";
import { Navigate } from "react-router-dom";
import { useAuth } from "../../../../providers/auth/AuthContext";

type PrivateRouteProps = {
  children: ReactNode;
};

const PrivateRoute: React.FC<PrivateRouteProps> = ({ children }) => {
  const { user, authInProgress } = useAuth();

  if (authInProgress) {
    return <div>Loading...</div>; // Or a spinner component
  }

  return user ? children : <Navigate to={`/auth/login?returnUrl=${encodeURIComponent(location.pathname)}`} />;
};

export default PrivateRoute;
