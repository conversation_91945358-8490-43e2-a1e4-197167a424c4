import { Modal } from "antd";
import { IFormElement, IFormSection } from "../types";

export const handleSectionDelete = (
    sections: IFormSection[],
    setSections: (sections: IFormSection[]) => void,
    selectedSection: string,
    setSelectedSection: (section: string) => void,
    setSelectedSubSection: (subSection: string) => void,
    sectionKey: string,
    sectionTitle?: string
) => {
    Modal.confirm({
        title: `Delete Section "${sectionTitle}"?`,
        content: `Deleting this section will also remove all of its associated subsections. This action cannot be undone. Are you sure you want to continue?`,
        okText: "Delete",
        okType: "danger",
        cancelText: "Cancel",
        centered: true,
        width: 600,
        onOk() {
            const filtered = sections.filter((section) => section.key !== sectionKey);
            setSections(filtered);

            if (selectedSection === sectionKey) {
                setSelectedSection('');
                setSelectedSubSection('');
            }
        },
    });
};

export const handleSubsectionDelete = (
    sections: IFormSection[],
    setSections: (sections: IFormSection[]) => void,
    selectedSubSection: string,
    selectedSection: string,
    setSelectedSubSection: (subSection: string) => void,
    setSelectedPath: (path: string) => void,
    subsectionKey: string,
    subsectionTitle?: string
) => {
    Modal.confirm({
        title: `Delete Subsection "${subsectionTitle}"?`,
        content: `This will permanently remove the subsection "${subsectionTitle}". This action cannot be undone. Do you wish to proceed?`,
        okText: "Delete",
        okType: "danger",
        cancelText: "Cancel",
        centered: true,
        width: 600,
        onOk() {
            const updatedSections = sections.map((section) => {
                if (section.key === selectedSection) {
                    return {
                        ...section,
                        children: section.children?.filter((child) => child.key !== subsectionKey) || [],
                    };
                }
                return section;
            });

            setSections(updatedSections);

            if (selectedSubSection === subsectionKey) {
                setSelectedSubSection('');
                setSelectedPath('');
            }
        },
    });
};

export const handleElementDelete = (
    elementToDelete: IFormElement,
    elements: IFormElement[],
    setElements: (elements: IFormElement[]) => void
) => {
    Modal.confirm({
        title: 'Delete Element?',
        content: 'This will permanently delete the selected element. This action cannot be undone. Do you want to continue?',
        okText: 'Delete',
        okType: 'danger',
        cancelText: 'Cancel',
        centered: true,
        width: 500,
        onOk: () => {
            const updatedElements = elements.filter(el => el !== elementToDelete);
            setElements(updatedElements);
        }
    });
};


