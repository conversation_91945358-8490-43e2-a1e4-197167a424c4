import React, { Suspense } from "react";
import { createBrowserRouter, RouteObject, RouterProvider } from "react-router-dom";
import { dashboardRoutes } from "./features/dashboard/routes"; // Dashboard-specific routes
import { authRoutes } from "./features/auth/routes"; // Auth-specific routes
import { adminRoutes } from "./features/admin/routes"; // Admin-specific routes
import { programAdminRoutes } from "./features/program-admin/routes";
import { preawardRoutes } from "./features/pre-award/routes";
import { PrivateRoute } from "./shared/components/common";

// Function to wrap private routes
const protectRoutes = (routes: RouteObject[]) =>
  routes.map(route => ({
    ...route,
    element: <PrivateRoute>{route.element}</PrivateRoute>,
  }));

// Create browser router configuration
const router = createBrowserRouter([
  ...authRoutes, // Public routes (e.g., login/signup)
  ...protectRoutes(dashboardRoutes), // Protect dashboard routes
  ...protectRoutes(adminRoutes), // Protect admin routes
  ...protectRoutes(programAdminRoutes), // protected program-admin routes
  ...protectRoutes(preawardRoutes), // protect pre-award routes
  // Fallback route for 404 errors
  {
    path: "*",
    element: <div>404: Page Not Found</div>,
  },
]);

const AppRouter: React.FC = () => (
  <Suspense fallback={<div>Loading...</div>}>
    <RouterProvider router={router} />
  </Suspense>
);

export default AppRouter;
