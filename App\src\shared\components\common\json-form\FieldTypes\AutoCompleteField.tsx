import React, { useEffect, useRef, useState } from "react";
import { FormField, FormOption } from "../types";
import styles from "./styles.module.css"

interface AutoCompleteFieldProps {
  field: FormField;
  value: string | number;
  onChange: (name: string, value: string | number) => void;
}

const AutoCompleteField: React.FC<AutoCompleteFieldProps> = ({ field, value, onChange }) => {
  const [query, setQuery] = useState("");
  const [showDropdown, setShowDropdown] = useState(false);
  const autoCompleteRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Sync initial value with query if editing
    if (value && Array.isArray(field.options)) {
      const selectedOption = field.options.find((option) => option.value === value);
      if (selectedOption) {
        setQuery(selectedOption.label);
      }
    }
  }, [value, field.options]); // Ensures proper sync on mount or option updates

  const filteredOptions = Array.isArray(field.options)
    ? field.options.filter((option) => option.label.toLowerCase().includes(query.toLowerCase()))
    : [];

  const handleSelect = (selected: FormOption) => {
    setQuery(selected.label); // Display label
    setShowDropdown(false);
    onChange(field.name, selected.value); // Store actual value
  };

  // Hide dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (autoCompleteRef.current && !autoCompleteRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className={styles.autoCompleteField} ref={autoCompleteRef}>
      <input
        type="text"
        id={field.name}
        name={field.name}
        value={query}
        className={styles.input}
        autoComplete="off"
        placeholder={`Select ${field.label}`}
        onChange={(e) => setQuery(e.target.value)}
        onFocus={() => setShowDropdown(true)}
      />
      {showDropdown && filteredOptions.length > 0 && (
        <ul className={styles.suggestionsList}>
          {filteredOptions.map((item) => (
            <li key={item.value} onClick={() => handleSelect(item)}>
              {item.label}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default AutoCompleteField;
