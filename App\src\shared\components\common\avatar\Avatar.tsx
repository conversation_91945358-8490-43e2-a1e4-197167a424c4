type Props = {
  id?: string;
  name?: string;
  url?: string;
  alt?: string;
  size?: number;
  onClick?: React.MouseEventHandler<HTMLButtonElement>;
};

const Avatar: React.FC<Props> = (props) => {
  const size = props.size ?? 32;
  const [firstName, lastName] = (props.name ?? '').split(' ');
  const intials = `${(firstName ?? "").charAt(0)}${(lastName ?? "").charAt(0)}`;

  return (
    <button
      id="user-menu-button"
      type="button"
      style={{ width: `${size}px`, height: `${size}px` }}
      className={`text-sm bg-gray-800 rounded-full md:me-0 focus:ring-4 focus:ring-gray-300 dark:focus:ring-gray-600`}
      aria-expanded="false"
      data-dropdown-toggle="user-dropdown"
      data-dropdown-placement="bottom"
      onClick={props.onClick}
    >
      {props.url ? (
        <img
          className='w-full h-full rounded-full'
          src={props.url}
          alt={props.alt} />
      ) : (
        <div className={`flex justify-center items-center w-full h-full rounded-full bg-primary text-white`}>{intials}</div>
      )}
    </button >
  );
};

export default Avatar;
