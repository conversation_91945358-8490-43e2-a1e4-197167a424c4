import React, { useEffect, useState } from "react";
import { OptionItem } from "./types";

interface EditableCellProps {
  editing: boolean;
  value: string | number;
  type?: "text" | "number" | "date" | "textarea" | "select";
  options?: OptionItem[] | (() => Promise<OptionItem[]>);
  onChange: (newValue: string) => void;
}

const EditableCell: React.FC<EditableCellProps> = ({ editing, value, type = "text", options, onChange }) => {
  const [dynamicOptions, setDynamicOptions] = useState<OptionItem[]>([]);

  useEffect(() => {
    if (typeof options === "function") {
      options()
        .then((fetchedOptions) => setDynamicOptions(fetchedOptions))
        .catch((error) => console.error("Error fetching options:", error));
    } else if (Array.isArray(options)) {
      setDynamicOptions(options);
    }
  }, [options]);

  return editing ? (
    <>
      {type === "textarea" ? (
        <textarea value={String(value)} onChange={(e) => onChange(e.target.value)} />
      ) : type === "select" ? (
        <select value={String(value)} onChange={(e) => onChange(e.target.value)}>
          {/* Default placeholder option */}
          <option value="" disabled>
            {`-Select-`}
          </option>
          {dynamicOptions.map((option) => (
            <option key={option.value} value={String(option.value)}>
              {option.label}
            </option>
          ))}
        </select>
      ) : (
        <input type={type} value={String(value)} onChange={(e) => onChange(e.target.value)} />
      )}
    </>
  ) : (
    <span>{value}</span>
  );
};

export default React.memo(EditableCell);
