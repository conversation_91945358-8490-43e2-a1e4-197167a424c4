import React from "react";
import { MasterPage } from "../../../../shared/components/render-elements";
import { ColumnSchema } from "../../../../shared/components/common/data-table/types";

interface PermissionMatrixProps {
    ModuleId: string;
}

// Define the column structure for the grid
const permissionSchema: ColumnSchema[] = [
  {
    key: "Id",
    title: "Id",
    hidden: true,
    editable: false
  },
  {
    
    key: "ModuleId",
    title: "Name",
    type: "select",
    filterable: true,
    editable: true,
    sorter: false
  },
  {
    key: "CanView",
    title: "View",
    type: "text", // assume toggle supported by your component library
    editable: true
  },
  {
    key: "CanAdd",
    title: "Add",
    type: "text",
    editable: true
  },
  {
    key: "CanEdit",
    title: "Edit",
    type: "text",
    editable: true
  },
  {
    key: "CanDelete",
    title: "Delete",
    type: "text",
    editable: true
  }
];

// Component that renders the permission matrix table
const PermissionMatrix: React.FC<PermissionMatrixProps> = ({ ModuleId }) => {
  return (
    <MasterPage
      entityName="Permission"
      columnsSchema={permissionSchema}
      mode="inline"
      apiEndpoints={{
        list: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/permission?ModuleId=${ModuleId}`,
        create: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/permission`,
        edit: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/permission`,
        delete: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/permission`
      }}
    />
  );
};

export default PermissionMatrix;
