import { useState } from "react";

export const usePagination = <T,>(data: T[], pageSize: number) => {
  const [currentPage, setCurrentPage] = useState(1);

  const paginatedData = data.slice((currentPage - 1) * pageSize, currentPage * pageSize);

  const nextPage = () => setCurrentPage((prev) => (prev * pageSize < data.length ? prev + 1 : prev));

  const prevPage = () => setCurrentPage((prev) => (prev > 1 ? prev - 1 : prev));

  return { paginatedData, currentPage, nextPage, prevPage };
};
