
import React, { useEffect, useState } from "react";
import { workflowDefinitionService, WorkflowCondition } from "../../../../../api/services/admin/workflowService";
import { PlusOutlined, DeleteOutlined } from "@ant-design/icons";

interface WorkflowConditionExtended extends WorkflowCondition {
  _fields?: { value: string; label: string }[];
  _values?: { value: string; label: string }[];
}

const WorkflowDefinition: React.FC = () => {
  const [formValues, setFormValues] = useState({
    Id: 0,
    Name: "",
    Description: "",
    Action: "",
    Table: ""
  });

  const [conditions, setConditions] = useState<Array<{
    id: number;
    data: Partial<WorkflowConditionExtended>;
    logicOperator: string;
  }>>([{ id: Date.now(), data: {}, logicOperator: "AND" }]);

  const [dropdowns, setDropdowns] = useState({
    actions: [] as { value: string; label: string }[],
    tables: [] as { value: string; label: string }[],
    operators: [] as { value: string; label: string }[]
  });

  useEffect(() => {
    const fetchDropdowns = async () => {
      const actions = await workflowDefinitionService.getActions?.();
      const tables = await workflowDefinitionService.getTables?.();
      const operators = await workflowDefinitionService.getOperators?.();
      setDropdowns({
        actions: actions || [],
        tables: tables || [],
        operators: operators || []
      });
    };
    fetchDropdowns();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormValues(prev => ({ ...prev, [name]: value }));
  };

  const handleConditionInputChange = async (id: number, e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    setConditions(prev =>
      prev.map(c =>
        c.id === id ? { ...c, data: { ...c.data, [name]: value } } : c
      )
    );

    if (name === "Table") {
      const fields = await workflowDefinitionService.getFields?.(value);
      setConditions(prev =>
        prev.map(c =>
          c.id === id
            ? { ...c, data: { ...c.data, Field: "", Value: "", Table: value, _fields: fields } }
            : c
        )
      );
    }

    if (name === "Field") {
      const selected = conditions.find(c => c.id === id);
      if (selected?.data.Table) {
        const values = await workflowDefinitionService.getValues?.(value);
        setConditions(prev =>
          prev.map(c =>
            c.id === id
              ? { ...c, data: { ...c.data, Value: "", Field: value, _values: values } }
              : c
          )
        );
      }
    }
  };

  const handleAddCondition = () => {
    setConditions(prev => [
      ...prev,
      { id: Date.now(), data: {}, logicOperator: "AND" }
    ]);
  };

  const handleRemoveCondition = (id: number) => {
    setConditions(prev => prev.filter(c => c.id !== id));
  };
  
  const handleLogicChange = (id: number, logicOperator: string) => {
    setConditions(prev =>
      prev.map(c => (c.id === id ? { ...c, logicOperator } : c))
    );
  };

  const handleSave = () => {
    const payload = {
      ...formValues,
      conditions: conditions.map(({ data, logicOperator }) => ({
        ...data,
        logicOperator
      }))
    };
    (window as any).setDefinitionData?.(payload);
  };

  useEffect(() => {
    (window as any).saveWorkflowDefinition = handleSave;
  }, [formValues, conditions]);

  return (
    <div className="flex flex-col flex-1 overflow-y-auto gap-2 p-8 font-roboto">
      {/* General Info Section */}
      <div>
        <div className="mb-4">
          <label className="block text-[14px] font-normal normal-case tracking-normal whitespace-pre text-[#46464C]">
            <span className="text-red-500 mr-1">*</span>
            Name
          </label>
          <input
            type="text"
            name="Name"
            required
            value={formValues.Name}
            onChange={handleInputChange}
            className="w-[250px] px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Workflow Name"
          />
        </div>

        <div className="mb-1">
          <label className="block text-[14px] font-normal normal-case tracking-normal whitespace-pre text-[#46464C]">
            <span className="text-red-500 mr-1">*</span>
            Description
          </label>
          <textarea
            name="Description"
            required
            value={formValues.Description}
            onChange={handleInputChange}
            className="w-[650px] px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Workflow Description"
            rows={3}
          />
        </div>
      </div>

      <div className="border-t border-gray-200 my-3"></div>

      {/* Trigger On Section */}
      <div>
        <h2 className="text-[18px] font-bold normal-case tracking-normal whitespace-pre text-[#46464C]">Trigger On</h2>
        <div className="grid grid-cols-2 w-[500px] gap-6 my-4">
          <div>
            <label className="block text-[14px] font-normal normal-case tracking-normal whitespace-pre text-[#46464C]">
              <span className="text-red-500 mr-1">*</span>
              Action
            </label>
            <select
              name="Action"
              required
              value={formValues.Action}
              onChange={handleInputChange}
              className="w-full px-4 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select Action</option>
              {dropdowns.actions.map(action => (
                <option key={action.value} value={action.value}>
                  {action.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-[14px] font-normal normal-case tracking-normal whitespace-pre text-[#46464C]">
              <span className="text-red-500 mr-1">*</span>
              Table
            </label>
            <select
              name="Table"
              required
              value={formValues.Table}
              onChange={handleInputChange}
              className="w-full px-4 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select Table</option>
              {dropdowns.tables.map(table => (
                <option key={table.value} value={table.value}>
                  {table.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      <div className="border-t border-gray-200 my-3"></div>

      {/* Conditions Section */}
      <div>
        <h2 className="text-[18px] font-bold normal-case tracking-normal whitespace-pre text-[#46464C] mb-4">
          Conditions
        </h2>

        {conditions.map((condition, index) => (
          <div
            key={condition.id}
          >
            <div className="flex items-end gap-4 my-4 flex-wrap">
              {/* Table */}
              <div className="min-w-[200px] w-[200px]">
                <label className="block text-[14px] text-[#46464C] mb-1">
                  <span className="text-red-500 mr-1">*</span>Table
                </label>
                <select
                  name="Table"
                  required
                  value={condition.data.Table || ""}
                  onChange={(e) => handleConditionInputChange(condition.id, e)}
                  className="w-full px-3 py-1 border border-gray-300 rounded-md text-sm"
                >
                  <option value="">Select Table</option>
                  {dropdowns.tables.map((table) => (
                    <option key={table.value} value={table.value}>
                      {table.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Field */}
              <div className="min-w-[200px] w-[200px]">
                <label className="block text-[14px] text-[#46464C] mb-1">
                  <span className="text-red-500 mr-1">*</span>Field
                </label>
                <select
                  name="Field"
                  required
                  value={condition.data.Field || ""}
                  onChange={(e) => handleConditionInputChange(condition.id, e)}
                  className="w-full px-3 py-1 border border-gray-300 rounded-md text-sm"
                >
                  <option value="">Select Field</option>
                  {(condition.data._fields || []).map((field) => (
                    <option key={field.value} value={field.value}>
                      {field.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Operator */}
              <div className="min-w-[200px] w-[200px]">
                <label className="block text-[14px] text-[#46464C] mb-1">
                  <span className="text-red-500 mr-1">*</span>Operator
                </label>
                <select
                  name="Operator"
                  required
                  value={condition.data.Operator || ""}
                  onChange={(e) => handleConditionInputChange(condition.id, e)}
                  className="w-full px-3 py-1 border border-gray-300 rounded-md text-sm"
                >
                  <option value="">Select Operator</option>
                  {dropdowns.operators.map((operator) => (
                    <option key={operator.value} value={operator.value}>
                      {operator.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Value */}
              <div className="min-w-[200px] w-[200px]">
                <label className="block text-[14px] text-[#46464C] mb-1">
                  <span className="text-red-500 mr-1">*</span>Value
                </label>
                <select
                  name="Value"
                  required
                  value={condition.data.Value || ""}
                  onChange={(e) => handleConditionInputChange(condition.id, e)}
                  className="w-full px-3 py-1 border border-gray-300 rounded-md text-sm"
                >
                  <option value="">Select Value</option>
                  {(condition.data._values || []).map((value) => (
                    <option key={value.value} value={value.value}>
                      {value.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Action buttons */}

              <div className="flex items-center gap-3 h-[30px]">
                {/* Show Add button only on the last condition */}
                {index === conditions.length - 1 && (
                  <button
                    type="button"
                    onClick={handleAddCondition}
                    title="Add condition"
                    className="w-6 h-6 rounded-full border border-blue-500 text-blue-500 flex items-center justify-center hover:bg-blue-100"
                  >
                    <PlusOutlined />
                  </button>
                )}

                {/* Show Delete & Logic Operator only before last condition */}
                {conditions.length > 1 && index < conditions.length - 1 && (
                  <>
                    <button
                      type="button"
                      onClick={() => handleRemoveCondition(condition.id)}
                      title="Remove condition"
                      className="w-6 h-6 rounded-full border border-red-500 text-red-500 flex items-center justify-center hover:bg-red-100"
                    >
                      <DeleteOutlined />
                    </button>
                    <select
                      value={condition.logicOperator}
                      onChange={(e) => handleLogicChange(condition.id, e.target.value)}
                      className="border border-gray-300 rounded px-2 py-1 text-sm bg-white"
                    >
                      <option value="AND">AND</option>
                      <option value="OR">OR</option>
                      <option value="NOT">NOT</option>
                    </select>
                  </>
                )}
              </div>

            </div>
          </div>
        ))}
      </div>



    </div>
  );
};

export default WorkflowDefinition;
