import React, { useState } from "react";
import { Table, Switch } from "antd";
import type { ColumnsType } from "antd/es/table";

interface PermissionNode {
  key: string;
  name: string;
  CanView: boolean;
  CanAdd: boolean;
  CanEdit: boolean;
  CanDelete: boolean;
  children?: PermissionNode[];
}

const PermissionMatrix: React.FC = () => {
  const [treeData, setTreeData] = useState<PermissionNode[]>([
    {
      key: "1",
      name: "Masters",
      CanView: false,
      CanAdd: false,
      CanEdit: false,
      CanDelete: false,
      children: [
        {
          key: "1-1",
          name: "Report Category",
          CanView: true,
          CanAdd: false,
          CanEdit: true,
          CanDelete: false,
        },
        {
          key: "1-2",
          name: "Project Phase",
          CanView: false,
          CanAdd: false,
          CanEdit: false,
          CanDelete: false,
        },
        {
          key: "1-3",
          name: "Funding Agency",
          CanView: true,
          CanAdd: true,
          CanEdit: false,
          CanDelete: false,
        },
      ],
    },
    {
      key: "2",
      name: "Transactions",
      CanView: false,
      CanAdd: false,
      CanEdit: false,
      CanDelete: false,
      children: [
        {
          key: "2-1",
          name: "Invoice Approval",
          CanView: true,
          CanAdd: false,
          CanEdit: false,
          CanDelete: false,
        },
        {
          key: "2-2",
          name: "Expense Submission",
          CanView: false,
          CanAdd: false,
          CanEdit: false,
          CanDelete: false,
        },
      ],
    },
  ]);

  const onTogglePermission = (key: string, field: keyof PermissionNode, value: boolean) => {
    const updateTree = (nodes: PermissionNode[]): PermissionNode[] =>
      nodes.map((node) => {
        if (node.key === key) {
          return { ...node, [field]: value };
        } else if (node.children) {
          return { ...node, children: updateTree(node.children) };
        }
        return node;
      });

    setTreeData((prev) => updateTree(prev));
  };

  const columns: ColumnsType<PermissionNode> = [
    {
      title: "Module",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "View",
      dataIndex: "CanView",
      key: "CanView",
      render: (value, record) => (
        <Switch
          checked={value}
          onChange={(checked) => onTogglePermission(record.key, "CanView", checked)}
        />
      ),
    },
    {
      title: "Add",
      dataIndex: "CanAdd",
      key: "CanAdd",
      render: (value, record) => (
        <Switch
          checked={value}
          onChange={(checked) => onTogglePermission(record.key, "CanAdd", checked)}
        />
      ),
    },
    {
      title: "Edit",
      dataIndex: "CanEdit",
      key: "CanEdit",
      render: (value, record) => (
        <Switch
          checked={value}
          onChange={(checked) => onTogglePermission(record.key, "CanEdit", checked)}
        />
      ),
    },
    {
      title: "Delete",
      dataIndex: "CanDelete",
      key: "CanDelete",
      render: (value, record) => (
        <Switch
          checked={value}
          onChange={(checked) => onTogglePermission(record.key, "CanDelete", checked)}
        />
      ),
    },
  ];

  return (
    <div style={{ padding: 16 }}>
      <h2 style={{ marginBottom: 16 }}>Permission Matrix</h2>
      <Table
        columns={columns}
        dataSource={treeData}
        rowKey="key"
        pagination={false}
        expandable={{ defaultExpandAllRows: true }}
        bordered
      />
    </div>
  );
};

export default PermissionMatrix;
