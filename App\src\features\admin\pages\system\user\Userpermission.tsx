import React from "react";
import { MasterPage } from "../../../../../shared/components/render-elements";
import { ColumnSchema } from "../../../../../shared/components/common/data-table/types";

interface UserPermissionListProps {
    userId: number;
}

const userPermissionSchema: ColumnSchema[] = [
  {
    key: "Id",
    title: "Id",
    hidden: true,
    editable: false,
    sorter: false
  },
  {
    key: "BusinessUnitId",
    title: "Business Unit",
    editable: true,
    sorter: true,
    filterable: true,
    type: "select"
  },
  
  {
    key: "RoleId",
    title: "Role",
    editable: true,
    sorter: true,
    filterable: true,
    type: "select"
  }
];

const UserPermissionList: React.FC<UserPermissionListProps> = ({ userId }) => {
  return (
    <MasterPage
      entityName="UserPermission"
      columnsSchema={userPermissionSchema}
      mode="modal"
      modal={{
        formSchema: {
          title: "User Permission",
          formGroups: [
            {
              title: "",
              columns: 2,
              fields: [
                {
                  name: "Id",
                  type: "hidden",
                  required: true,
                  defaultValue: 0
                },
                {
                  name: "UserId",
                  type: "hidden",
                  required: true,
                  defaultValue: userId
                },
                {
                  name: "BusinessUnitId",
                  type: "select",
                  label: "BusinessUnit",
                  required: true
                },
                {
                  name: "RoleId",
                  type: "select",
                  label: "Role",
                  required: false
                },
                {
                    name: "ModuleId",
                    type: "hidden",
                    label: "Module",
                    defaultValue:1,
                    required: false
                  },
              ]

            }
          ]
        },
        width: 50
      }}
      apiEndpoints={{
        create: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/user-permissions`,
        edit: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/user-permissions`,
        delete: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/user-permissions`,
        list: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/user-permissions?id=${userId }`
      }}
    />
  );
};

export default UserPermissionList;