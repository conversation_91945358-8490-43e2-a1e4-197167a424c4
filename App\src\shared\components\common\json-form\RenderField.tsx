import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";
import { FormField, FormOption } from "./types";
import { AutoCompleteField, CheckboxField, CurrencyField, DateField, FileField, HiddenField, MaskedField, MultiselectField, RadioField, RatingField, SelectField, TextAreaField, TextField, ToggleField, TreeMultiSelectField, TreeSelectField } from "./FieldTypes";
import { useFieldLogic } from "./hooks";
import styles from "./styles.module.css"
import { fetchOptions } from "./utils/fetchOptions";

interface RenderFieldProps {
  field: FormField;
  formData: Record<string, any>;
  error?: string;
  onChange: (name: string, value: any) => void;
}

const RenderField: React.FC<RenderFieldProps> = ({ field, formData, error, onChange }) => {
  const { shouldRenderField, resolveFieldDependencies } = useFieldLogic(formData);
  const [options, setOptions] = useState<FormOption[]>([]);

  // Memoized function to prevent unnecessary re-fetches
  const fetchFieldOptions = useCallback(async () => {
    try {
      if (field.optionsFromUrl) {
        const fetchedOptions = await fetchOptions(field.optionsFromUrl);
        setOptions(fetchedOptions.map(x => ({label: x.Name, value: x.Id} as FormOption)) ?? []);
      } else if (typeof field.options === "function") {
        // If dependency exists, pass its value to the function, otherwise fetch without dependency
        const dependentValue = field.dependency ? formData[field.dependency] : undefined;
        const optionsData = await field.options(formData, dependentValue); // Pass dependency if needed
        setOptions(optionsData ?? []);
      } else {
        // Fallback to hard-coded options if dependency is null/undefined
        setOptions(field.options ?? []);
      }
    } catch (error) {
      console.error(`Error fetching options for field "${field.name}":`, error);
    }
  }, [field.optionsFromUrl, field.options, field.dependency ? formData[field.dependency] : null]);

  useEffect(() => {
    fetchFieldOptions();
  }, [fetchFieldOptions]);

  if (!shouldRenderField(field)) {
    return null; // Skip rendering if conditionalRender returns false
  }

  const resolvedOptions = resolveFieldDependencies(field);

  if (field.type === "hidden") {
    return <HiddenField field={field} value={formData[field.name] || ""} onChange={(value: any) => onChange(field.name, value)} />
  }

  return (
    <div key={field.name} className="form-field" style={{ gridColumn: `span ${field.columnSpan || 1}` }}>
      <label htmlFor={field.name}>
        <div>
          {field.label}: {field.required && <span className={styles.mandatory}>*</span>}
        </div>
        {field.tooltip && (
          <div className={styles.tooltipWrapper}>
            <span className={styles.infoIcon}>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="#1e2939"
                strokeWidth="1"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <circle cx="12" cy="12" r="10" stroke="#1e2939"></circle>
                <line x1="12" y1="16" x2="12" y2="12" strokeWidth="2"></line>
                <line x1="12" y1="8" x2="12" y2="8" strokeWidth="2"></line>
              </svg>
            </span>
            <div className={styles.tooltipContent}>{field.tooltip}</div>
          </div>
        )}
      </label>

      {/* Render specific field types */}
      {field.type === "text" || field.type === "email" || field.type === "number" || field.type === "password" ? (
        <TextField field={field} value={formData[field.name] || ""} onChange={onChange} />
      ) : null}

      {field.type === "currency" ? (
        <CurrencyField field={field} value={formData[field.name] || ""} onChange={onChange} />
      ) : null}

      {field.type === "textarea" ? (
        <TextAreaField field={field} value={formData[field.name] || ""} onChange={onChange} />
      ) : null}

      {field.type === "masked" ? (
        <MaskedField field={field} value={formData[field.name] || ""} onChange={onChange} />
      ) : null}

      {field.type === "select" ? (
        <SelectField
          field={{ ...field, options: options ?? resolvedOptions }} // TODO: Pass resolved or fallback options
          value={formData[field.name] || ""}
          onChange={onChange}
        />
      ) : null}

      {field.type === "autocomplete" ? (
        <AutoCompleteField
          field={{ ...field, options: options ?? resolvedOptions }} // TODO: Pass resolved or fallback options
          value={formData[field.name] || ""}
          onChange={onChange}
        />
      ) : null}

      {field.type === "multiselect" ? (
        <MultiselectField
          field={{ ...field, options: options ?? resolvedOptions }}
          value={formData[field.name] || []}
          onChange={(name, selectedValues) => onChange(name, selectedValues)}
        />
      ) : null}

      {field.type === "treeSelect" ? (
        <TreeSelectField
          field={{ ...field, options: options ?? resolvedOptions }}
          value={formData[field.name] || []}
          onChange={onChange}
        />
      ) : null}

      {field.type === "treeMultiSelect" ? (
        <TreeMultiSelectField
          field={{ ...field, options: options ?? resolvedOptions }}
          value={formData[field.name] || []}
          onChange={onChange}
        />
      ) : null}

      {field.type === "checkbox" ? (
        <CheckboxField field={field} value={!!formData[field.name]} onChange={onChange} />
      ) : null}

      {field.type === "radio" ? (
        <RadioField
          field={{ ...field, options }} // TODO: Pass resolved or fallback options
          value={formData[field.name] || ""}
          onChange={onChange}
        />
      ) : null}

      {field.type === "date" ? (
        <DateField field={field} value={formData[field.name] || ""} onChange={onChange} />
      ) : null}

      {field.type === "file" ? (
        <FileField field={field} onChange={onChange} />
      ) : null}

      {field.type === "widget" && field.widget ? (
        <field.widget
          {...field.widgetProps}
          value={formData[field.name]}
          onChange={(value: any) => onChange(field.name, value)}
        />
      ) : null}

      {field.type === "toggle" ? (
        <ToggleField field={field} value={formData[field.name] || false} onChange={onChange} />
      ) : null}

      {field.type === "rating" ? (
        <RatingField field={field} value={formData[field.name] || 0} onChange={onChange} />
      ) : null}

      {error && <span className={styles.errorMessage}>{error}</span>} {/* Display validation error */}
    </div>
  );
};

export default RenderField;
