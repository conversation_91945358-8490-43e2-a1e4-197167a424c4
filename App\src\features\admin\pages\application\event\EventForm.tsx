import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { FormOption, JForm, JFormSchema } from "../../../../../shared/components/common/json-form";
import { message } from "antd";
import { Loader } from "../../../../../shared/components/common";
import { Event, eventService } from "../../../../../api/services/admin/eventService";

const EventForm: React.FC<{ mode: "create" | "edit" }> = ({ mode }) => {
  const navigate = useNavigate();
  const { id } = useParams(); // Get event ID from URL params for edit mode
  const [formData, setFormData] = useState<Event | null>(null);
  const eventId = Number(id);

  const schema: JFormSchema = {
    title: `${ mode } Event`,
    formGroups: [
      {
        title: "",
        columns: 4,
        fields: [
          {
            name: "Id",
            type: "hidden",
            required: true,
            defaultValue: 0
          },
          {
            name: "Code",
            type: "text",
            label: "Code",
            required: true,
            validation: {
              required: true,
              maxLength: 5
            }
          },          
          {
            name: "Name",
            type: "text",
            label: "Name",
            required: true,
            validation: {
              required: true,
              maxLength: 100
            }
          },
          {
            name: "Description",
            type: "text",
            label: "Description",
            required: true,
            validation: {
              required: true,
              maxLength: 500
            }
          },
          {
            name: "EventTypeId",
            type: "select",
            label: "EventType",
            required: true,
            options: async (): Promise<FormOption[]> => {
              const response = await eventService.fetchAll();
              return response!.EventTypeId.map(eventType => ({
                label: eventType.DisplayText,
                value: eventType.Value
              }));
            },
          }
        ]
      }
    ]
  };

  useEffect(() => {
    if (mode === "edit" && eventId) {
      eventService.getById(eventId)
        .then(setFormData)
        .catch((error) => console.error("Error fetching event data:", error));
    }
  }, [mode, eventId]);
 
  const handleSubmit = async (formValues: Record<string, any>) => {
    try {
      const response = mode === "edit" && eventId
        ? await eventService.update(eventId, formValues)
        : await eventService.create(formValues);
 
      if (response) {
        message.success(`Event ${mode === "edit" ? "updated" : "created"} successfully.`);
        navigate("..");
      } else {
        message.error(`Failed to ${mode === "edit" ? "update" : "create"} event. Please try again.`);
      }
    } catch (error: any) {
      console.error("Error submitting form:", error);
      message.error(error?.response?.data?.message || "An unexpected error occurred.");
    }
  };

  return (
    <div className="flex flex-col gap-2 bg-white p-[30px] rounded-[6px]">
      {mode === "edit" && !formData ? (
        <Loader />
      ) : (
        <JForm schema={schema} onSubmit={handleSubmit} data={formData || {}} />
      )}
    </div>
  );
};

export default EventForm;
