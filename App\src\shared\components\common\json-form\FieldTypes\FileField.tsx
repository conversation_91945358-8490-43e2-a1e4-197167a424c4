import React, { useState } from "react";
import { FormField } from "../types";
import styles from "./styles.module.css";

interface FileFieldProps {
  field: FormField;
  onChange: (name: string, files: File[] | File | null) => void;
  multiple?: boolean;
  accept?: string;
  maxSize?: number;
}

const FileField: React.FC<FileFieldProps> = ({ field, onChange, multiple = false, accept, maxSize = 10 }) => {
  const [files, setFiles] = useState<File[]>([]);
  const [errorMessage, setErrorMessage] = useState("");

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(e.target.files || []);

    const validFiles = selectedFiles.filter((file) => {
      if (file.size / (1024 * 1024) > maxSize) {
        setErrorMessage(`File ${file.name} exceeds the ${maxSize}MB limit.`);
        return false;
      }
      return true;
    });

    if (multiple) {
      setFiles(validFiles);
      onChange(field.name, validFiles);
    } else {
      const singleFile = validFiles.length > 0 ? validFiles[0] : null;
      setFiles(singleFile ? [singleFile] : []);
      onChange(field.name, singleFile);
    }
  };

  const handleRemoveFile = (index: number) => {
    const updatedFiles = files.filter((_, i) => i !== index);
    setFiles(updatedFiles);
    onChange(field.name, multiple ? updatedFiles : updatedFiles[0] || null);
  };

  return (
    <div className={styles.fileField}>
      <label className={styles.uploadLabel}>
        <input
          type="file"
          name={field.name}
          accept={accept}
          multiple={multiple}
          onChange={handleFileChange}
          required={field.required}
          className={styles.hiddenInput}
        />
        <span className={styles.attachIcon}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="18"
            height="18"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M21.44 11.05a5.5 5.5 0 0 1-7.78 7.78L5.5 10.67a3.93 3.93 0 0 1 5.56-5.56l6.72 6.72"></path>
          </svg>
        </span>
        <span>Choose File...</span>
      </label>

      {files.length > 0 && (
        <ul className={styles.fileList}>
          {files.map((file, index) => (
            <li key={index}>
              <span>{file.name}</span>
              <button type="button" className={styles.removeButton} onClick={() => handleRemoveFile(index)}>
                &#215;
              </button>
            </li>
          ))}
        </ul>
      )}

      {errorMessage && <span className={styles.error}>{errorMessage}</span>}
    </div>
  );
};

export default FileField;
