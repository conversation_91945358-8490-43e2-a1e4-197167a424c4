.panelContainer {
  display: flex;
  gap: 1rem;
  min-height: calc(100vh - 150px);
  background-color: linear-gradient(to bottom, #d5e7f1, #f2f2f2);
}

.leftPanel {
  flex: 0 0 200px;
  max-width: 220px;
  background: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
  font-size: 14px;
  font-weight: bold;
  padding: 30px 0 20px 0;
  overflow-y: auto;
  color: #666;
}

.navList {
  list-style: none;
  margin: 0;
  padding: 0;
}

.navItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.navItem:hover {
  background-color: #f5f5f5;
}

.activeNavItem {
  background-color: #e5e5e5;
  color: #00AEEF; /* This affects the label text */
}

.indexBox {
  width: 20px;
  height: 20px;
  min-width: 20px;
  border: 2px solid #00AEEF;
  color: #00AEEF;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  font-size: 12px;
}

