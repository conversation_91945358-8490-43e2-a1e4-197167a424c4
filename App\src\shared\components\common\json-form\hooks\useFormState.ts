import { useEffect, useState } from "react";
import { FormField } from "../types";

const useFormState = (
  initialState: Record<string, any> = {},
  formFields: FormField[], // Include schema fields for transformation lookup
  onFormChange?: (formData: Record<string, any>) => void,
  transformError?: (errorMessage: string, fieldName: string) => string
) => {
  // Centralized function for initializing form data with default values.
  const initializeFormData = (): Record<string, any> => {
    return formFields.reduce((acc, field) => {
      const fieldValue =
        initialState && initialState[field.name] !== undefined
          ? initialState[field.name]
          : typeof field.defaultValue === "function"
            ? field.defaultValue(initialState)
            : field.defaultValue;
      acc[field.name] = fieldValue ?? "";
      return acc;
    }, {} as Record<string, any>);
  };

  // Set state initially using the initialization function.
  const [formData, setFormData] = useState<Record<string, any>>(initializeFormData());
  const [errors, setErrors] = useState<Record<string, string | undefined>>({});

  useEffect(() => {
    if (onFormChange) {
      const handler = setTimeout(() => {
        onFormChange(formData);
      }, 300); // Debounced to improve performance
      return () => clearTimeout(handler);
    }
  }, [formData]);

  // Function to apply field transformations before updating state
  const applyTransform = (name: string, value: any) => {
    const field = formFields.find((f) => f.name === name);
    return field?.transform ? field.transform(value, formData) : value;
  };

  // Updates a specific field in the form state with transformation logic
  const updateField = (name: string, value: any) => {
    const transformedValue = applyTransform(name, value);
    setFormData((prev) => ({ ...prev, [name]: transformedValue }));
    setErrors((prev) => ({ ...prev, [name]: undefined }));
  };

  // Function to handle validation errors
  const setFieldErrors = (newErrors: Record<string, string>) => {
    const transformedErrors = Object.entries(newErrors).reduce((acc, [fieldName, errorMessage]) => {
      acc[fieldName] = transformError ? transformError(errorMessage, fieldName) : errorMessage;
      return acc;
    }, {} as Record<string, string>);
    setErrors(transformedErrors);
  };

  // Resets the entire form state
  const resetForm = () => {
    setFormData(initialState);
    setErrors({});
  };

  return {
    formData,
    errors,
    updateField,
    setFormData,
    setFieldErrors,
    resetForm,
  };
};

export default useFormState;
