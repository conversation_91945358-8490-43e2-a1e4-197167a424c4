import React from "react";
import { MasterPage } from "../../../../shared/components/render-elements";
import { ColumnSchema } from "../../../../shared/components/common/data-table";

const businessUnitSchema: ColumnSchema[] = [
  { key: "Id", title: "Id", hidden: true, editable: false, sorter: false },
  {
    key: "Code",
    title: "Code",
    editable: true,
    type: "text",
    sorter: true,
    filterable: true,
  },
  {
    key: "Name",
    title: "Name",
    editable: true,
    type: "text",
    sorter: true,
    filterable: true,
  },
  {
    key: "OBSSettingId",
    title: "OBS Setting",
    editable: true,
    type: "select",
    sorter: true,
    filterable: true,
  },
  {
    key: "ParentBusinessUnitId",
    type: "number",
    title: "Parent Business Unit",
    filterable: true,
    editable: true,
  },
  {
    key: "Active",
    title: "Active",
    editable: true,
    type: "select",
    sorter: true,
    filterable: true,
    options: [
      { value: 1, label: "Yes" },
      { value: 0, label: "No" },
    ],
  },
];

const BusinessUnitList: React.FC = () => {
  return (
    <MasterPage
      entityName="Business Unit"
      columnsSchema={businessUnitSchema}
      mode="modal"
      modal={{
        formSchema: {
          title: "Business Unit Management",
          formGroups: [
            {
              title: "",
              columns: 2,
              fields: [
                { name: "Code", type: "text", label: "Code", required: true },
                { name: "Name", type: "text", label: "Name", required: true },
                {
                  name: "OBSSettingId",
                  type: "select",
                  label: "OBS Setting",
                  required: true,
                },

                {
                  name: "ParentBusinessUnitId",
                  type: "treeSelect",
                  label: "Parent Business Unit",
                },
                {
                  name: "Active",
                  type: "select",
                  label: "Active",
                  required: true,
                  options: [
                    { value: 1, label: "Yes" },
                    { value: 0, label: "No" },
                  ],
                },
              ],
            },
          ],
        },
        width: 60,
      }}
      apiEndpoints={{
        create: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/business-units`,
        edit: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/business-units`,
        delete: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/business-units`,
        list: `${import.meta.env.VITE_Admin_API_BASE_URL}/api/business-units`,
      }}
    />
  );
};

export default BusinessUnitList;
